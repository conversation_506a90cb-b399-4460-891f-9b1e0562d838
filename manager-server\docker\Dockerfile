FROM python:3.9-bullseye

RUN sed -i "s@http://deb.debian.org@http://mirrors.aliyun.com@g" /etc/apt/sources.list
RUN sed -i "s@http://security.debian.org@http://mirrors.aliyun.com@g" /etc/apt/sources.list
RUN ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN apt-get update && apt-get install -y libcurl4-openssl-dev libffi-dev libxml2-dev libmariadb-dev g++ ffmpeg\
  && pip3 install bson tornado==6.0.2 sqlalchemy mysqlclient pymysql pika==0.13.1 ujson pycurl redis hiredis pycryptodome pillow cryptography bcrypt langchain==0.1.0 langchain-openai==0.0.2 openai==1.6.1 Cython pandas openpyxl xlsxwriter httpx[http2]==0.24.1 nest_asyncio anthropic wechatpayv3 wechatpy==2.0.0a26 qrcode tiktoken jinja2 alibabacloud_alimt20181012 curl_cffi --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn

ADD ./docker/entrypoint.sh /entrypoint.sh
ADD ./docker/wait-for-it.sh /wait-for-it.sh
RUN sed -i 's/\r$//' /entrypoint.sh && sed -i 's/\r$//' /wait-for-it.sh && chmod +x /entrypoint.sh && chmod +x /wait-for-it.sh

ADD ./server /server

ENTRYPOINT ["/entrypoint.sh"]

CMD ["python3", "/server/server.py"]

