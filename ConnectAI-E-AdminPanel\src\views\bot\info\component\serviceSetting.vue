<template>
  <div
    v-if="loading"
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <n-space vertical>
      <n-skeleton height="40px" width="33%" :sharp="false" />
      <n-skeleton height="60px" :sharp="false" />
      <n-skeleton height="60px" />
      <n-skeleton height="60px" />
      <n-skeleton height="40px" width="100px" :sharp="false" />
    </n-space>
  </div>
  <div
    v-else
    class="p-4 pb-2 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <h3 class="text-xl font-semibold dark:text-white mb-4">{{ t('message.my.fwpz') }}</h3>
    <div class="flex flex-wrap content-start items-start gap-[20px]">
      <div
        v-for="item in client"
        :key="item.id"
        class="max-w-sm bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow"
      >
        <div class="p-4 rounded-t-lg w-[355px]" alt="product image">
          <div class="flex justify-between items-center gap-2 mb-8">
            <div class="flex justify-start items-center gap-2">
              <component :is="iconRender({ localIcon: item.platform })" class="text-36px" />
              <h5 class="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">{{ item.name }}</h5>
            </div>
            <div>
              <n-tag v-if="item.tenant_status === null" round :bordered="false" class="cursor-pointer">
                {{ t('message.my.dpz') }}
                <template #icon>
                  <n-icon :component="StopCircleSharp" />
                </template>
              </n-tag>
              <n-tag v-else round :bordered="false" type="success" class="cursor-pointer">
                {{ t('message.my.ypz') }}
                <template #icon>
                  <n-icon :component="CheckmarkCircle" />
                </template>
              </n-tag>
            </div>
          </div>
          <div class="flex justify-end items-center gap-2">
            <div
              v-if="item.tenant_status === null"
              class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click.stop="handleDetail(item.id)"
            >
              {{ t('message.market.ljpz') }}
            </div>
            <div
              v-else
              class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click.stop="handleDetail(item.id)"
            >
              {{ t('message.my.xgpz') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <n-modal v-model:show="show" :mask-closable="true" :close-on-esc="true" :auto-focus="false">
    <StepFour v-model:data="data" :re-install="true" :app="{ id: appId }" @close="show = false" />
  </n-modal>
</template>

<script setup lang="ts">
import { onMounted, ref, provide } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import ClipboardJS from 'clipboard';
import { CheckmarkCircle, StopCircleSharp } from '@vicons/ionicons5';
import { useIconRender } from '@/composables';
import { getAppClient, getAppClientBot } from '@/service/api/app';
import StepFour from '@/views/bot/my/components/botInstall/stepFour/index.vue';
import { t } from '@/locales';
provide('close', close);

const { iconRender } = useIconRender();
const route = useRoute();
const appId = ref('');
const client = ref<ApiApp.AppClient[]>([]);
const data = ref<ApiApp.AppClientBot>({} as ApiApp.AppClientBot);
const show = ref(false);

const props = defineProps<{
  loading: boolean;  
  appInfo: ApiApp.AppInfo;
}>();
async function fetchData() {
  const {
    data: { data: clientData }
  } = await getAppClient({ id: route.query.id as string });
  client.value = clientData;
  appId.value = route.query.id;
}

function popupwindow(url, title, w, h) {
  var left = (screen.width/2)-(w/2);
  var top = (screen.height/2)-(h/2);
  return window.open(url, title, 'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width='+w+', height='+h+', top='+top+', left='+left);
}

const message = useMessage();

async function handleDetail(id: string) {
  const {
    data: { data: clientBot }
  } = await getAppClientBot({ id: route.query.id as string, botId: id });
  console.log('clientBot', clientBot)
  if (clientBot.platform == 'wxwork') {
    const instanceId = props.appInfo.id
    const authUrl = `${import.meta.env.PROD === false ? '/api' : ''}/api/wxwork/auth/${instanceId}/${clientBot.id}`;
    const win = popupwindow(authUrl, t('message.my.wxwork_auth_title'), 760, 560)
    window[`auth_callback_${clientBot.id}`] = function() {
      console.log('auth_callback', arguments)
      message.success(t('message.my.wxwork_auth_success'));
      win.close()
    }
  } else {
    data.value = clientBot;
    show.value = true;
  }
}

function close() {
  show.value = false;
}

onMounted(() => {
  fetchData();
  // eslint-disable-next-line no-new
  new ClipboardJS('.copy-btn');
});
</script>

<style scoped></style>
