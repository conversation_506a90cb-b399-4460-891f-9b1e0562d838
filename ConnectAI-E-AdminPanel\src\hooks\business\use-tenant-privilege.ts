import { ref, reactive, onMounted } from 'vue';
import { fetchTenantHandler } from '@/service';

const handler = ref();
const product = ref('');
const expired = ref('');

const fetchPromise = ref();

export const refreshHandler = () => {
  // 用fetchPromise.value做一下去除重复请求的目的，但是超过一定时间就移除，避免不刷新
  if (!fetchPromise.value) {
    fetchPromise.value = fetchTenantHandler().then(res => {
      handler.value = new Set(res.data.data);
      product.value = res.data.product;
      expired.value = res.data.expired;
    });
    setTimeout(() => (fetchPromise.value = null), 1000);
  }
  return fetchPromise;
};

export default function useTenantPrivilege() {
  onMounted(() => {
    if (!handler.value) {
      refreshHandler();
    }
  });

  // TODO mock allow
  const allow = action => {
    return handler.value && handler.value.has(action);
  };

  return {
    allow,
    deny: action => !allow(action),
    isSubscribe: () => Boolean(product.value),
    needUpgrade: () => (product.value === 'preview' || product.value === 'person') && product.value !== 'privatization',
    expiredDate: () => expired.value.split(' ')[0],
    expired: () => expired.value
  };
}
