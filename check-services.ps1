# ConnectAI Service Status Check Script
# Usage: .\check-services.ps1

Write-Host "=== ConnectAI Service Status Check ===" -ForegroundColor Green
Write-Host ""

# Check Docker Compose service status
Write-Host "1. Docker Compose Service Status:" -ForegroundColor Yellow
try {
    docker-compose -f docker-compose.local.yml ps
} catch {
    Write-Host "Error: Cannot get Docker Compose service status" -ForegroundColor Red
}
Write-Host ""

# Check main HTTP services
Write-Host "2. HTTP Service Health Check:" -ForegroundColor Yellow

# Check admin panel (nginx proxy)
Write-Host "Admin Panel (nginx proxy): " -NoNewline
try {
    $response = Invoke-WebRequest -Uri "http://localhost/" -Method Head -TimeoutSec 5 -ErrorAction Stop
    Write-Host "OK ($($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "ERROR" -ForegroundColor Red
}

# Check admin panel direct access
Write-Host "Admin Panel (direct): " -NoNewline
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/" -Method Head -TimeoutSec 5 -ErrorAction Stop
    Write-Host "OK ($($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "ERROR" -ForegroundColor Red
}

# Check manager service
Write-Host "Manager Service API: " -NoNewline
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/" -Method Head -TimeoutSec 5 -ErrorAction Stop
    Write-Host "OK ($($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "ERROR" -ForegroundColor Red
}

# Check knowledge base service
Write-Host "Knowledge Base Service: " -NoNewline
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/" -Method Head -TimeoutSec 5 -ErrorAction Stop
    Write-Host "OK ($($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "ERROR" -ForegroundColor Red
}

Write-Host ""

# Check database services
Write-Host "3. Database Service Check:" -ForegroundColor Yellow

# Check MySQL
Write-Host "MySQL: " -NoNewline
try {
    $mysqlResult = docker exec connectai-mysql mysql -u root -proot123 -e "SELECT 1;" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OK" -ForegroundColor Green
    } else {
        Write-Host "ERROR" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR" -ForegroundColor Red
}

# Check Redis
Write-Host "Redis: " -NoNewline
try {
    $redisResult = docker exec connectai-redis redis-cli ping 2>$null
    if ($redisResult -eq "PONG") {
        Write-Host "OK" -ForegroundColor Green
    } else {
        Write-Host "ERROR" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR" -ForegroundColor Red
}

# Check Elasticsearch
Write-Host "Elasticsearch: " -NoNewline
try {
    $response = Invoke-WebRequest -Uri "http://localhost:9200/_cluster/health" -TimeoutSec 5 -ErrorAction Stop
    $health = ($response.Content | ConvertFrom-Json).status
    if ($health -eq "green" -or $health -eq "yellow") {
        Write-Host "OK ($health)" -ForegroundColor Green
    } else {
        Write-Host "ERROR ($health)" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR" -ForegroundColor Red
}

# Check RabbitMQ
Write-Host "RabbitMQ: " -NoNewline
try {
    $credentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("guest:guest"))
    $headers = @{Authorization = "Basic $credentials"}
    $response = Invoke-WebRequest -Uri "http://localhost:15672/api/overview" -Headers $headers -TimeoutSec 5 -ErrorAction Stop
    Write-Host "OK ($($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "ERROR" -ForegroundColor Red
}

Write-Host ""

# Show container resource usage
Write-Host "4. Container Resource Usage:" -ForegroundColor Yellow
try {
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
} catch {
    Write-Host "Error: Cannot get container resource usage" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Check Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "Access URLs:" -ForegroundColor Cyan
Write-Host "  Admin Panel: http://localhost/" -ForegroundColor White
Write-Host "  Admin Panel (direct): http://localhost:8080/" -ForegroundColor White
Write-Host "  Manager Service: http://localhost:3000/" -ForegroundColor White
Write-Host "  Knowledge Base: http://localhost:8000/" -ForegroundColor White
Write-Host "  RabbitMQ Management: http://localhost:15672/" -ForegroundColor White
Write-Host "  Elasticsearch: http://localhost:9200/" -ForegroundColor White
