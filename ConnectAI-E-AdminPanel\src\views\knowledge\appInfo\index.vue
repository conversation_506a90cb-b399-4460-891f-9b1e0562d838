<template>
  <div class="w-full">
    <div class="grid grid-cols-1 px-4 pt-6 xl:grid-cols-12 xl:gap-4 overflow-auto h-full">
      <div class="col-span-full xl:col-span-4 gap-10">
        <HeadInfo :loading="loading" :data="setting" :app-info="appInfo" />
        <ResourceInfo
          v-model:data="setting"
          :loading="loading"
          :resources="resources"
          is-dataset
          @change="handleResourceChange"
        />
        <ChatHistory v-model:data="setting" :loading="loading" />
        <AdminInfo :loading="loading" />
      </div>
      <!-- Right Content -->
      <div class="col-span-8">
        <div
          v-if="loading"
          class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
        >
          <n-space vertical>
            <n-skeleton height="40px" width="33%" :sharp="false" />
            <n-skeleton height="60px" :sharp="false" />
            <n-skeleton height="60px" />
            <n-skeleton height="60px" />
            <n-skeleton height="40px" width="100px" :sharp="false" />
          </n-space>
        </div>
        <ServiceSetting :loading="loading" :app-info="appInfo" />
        <BotRightInfo v-model:data="setting" :loading="loading" :models="models" :resources="resources" />
        <Dataset v-model:data="setting" :loading="loading" />
        <Prompt v-model:data="setting" :loading="loading" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue';
import { useRoute } from 'vue-router';
import { isEmpty, toPairs } from 'lodash-es';
import { useLoading } from '@/hooks';
import { getAppResources } from '@/service/api/app';
import { getAppSetting, getAppInfo } from '@/service/api/messenger';
import HeadInfo from './component/headInfo.vue';
import AdminInfo from './component/adminInfo.vue';
import ResourceInfo from './component/resourceInfo.vue';
import BotRightInfo from './component/botRightInfo.vue';
import ServiceSetting from './component/serviceSetting.vue';
import Dataset from './component/dataset.vue';
import Prompt from './component/prompt.vue';
import ChatHistory from './component/chatHistory.vue';
import { getModelPermission } from '@/views/bot/info/utils';
const route = useRoute();
const { loading, startLoading, endLoading } = useLoading();
const resources = ref<ApiApp.AppResources[]>([]);

const models = ref<ApiApp.AppResource['models']>([]);
const setting = ref<ApiApp.APPSetting>({
  resource_id: '',
  resource_ids: {},
  group_permission: [],
  user_permission: [],
  prompt_id: [],
  sensitive_id: [],
  collection_id: [],
  prompt: '',
  chat_history: 'enable'
} as ApiApp.APPSetting);
const appInfo = ref<ApiApp.AppInfo>({} as ApiApp.AppInfo);

watch(
  () => route.query.id,
  (instance_id) => {
    if (instance_id) {
      getSetting(instance_id);
    }
  },
  {
    immediate: true
  }
);

async function getSetting(instance_id: string) {
  startLoading();
  try {
    const res = await getAppSetting({ id: instance_id });
    const {
      data: { data: appInfoData }
    } = await getAppInfo({ id: instance_id });
    const {
      data: { data: resourcesData }
    } = await getAppResources({ id: appInfoData.application_id });
    resources.value = resourcesData;
    appInfo.value = appInfoData;
    const formatSettion = handleSetting(res.data!.data, resourcesData);
    console.log('Dogtiti ~ file: index.vue:118 ~ getSetting ~ formatSettion:', formatSettion);
    setting.value = formatSettion;
    endLoading();
  } catch (err) {}
}

function handleSetting(setting: ApiApp.APPSetting, resourcesData: ApiApp.AppResources[]) {
  // 解构 setting 对象
  const { group_permission, user_permission, resource_ids } = setting;

  // 如果 resources 不为空
  // 如果 group_permission 为空，创建它
  if (isEmpty(group_permission)) {
    setting.group_permission = isEmpty(resource_ids)
      ? group_permission
      : toPairs(resource_ids)
          .map(([scene, resource_id]) => {
            // 查找 resource_id 对应的模型
            const { models, name, description } =
              resourcesData.find((r) => r.scene === scene)?.resource?.find((r) => r.id === resource_id) || {};
            return models
              ? getModelPermission(models, resource_id, 'allow_groups', 'deny_groups', name, description)
              : [];
          })
          .flat();
  } else {
    // 如果 group_permission 不为空，更新 flag
    setting.group_permission.forEach((item) => {
      item.flag = item.allow_groups.length > 0;
    });
  }

  // 如果 user_permission 为空，创建它
  if (isEmpty(user_permission)) {
    setting.user_permission = isEmpty(resource_ids)
      ? user_permission
      : toPairs(resource_ids)
          .map(([scene, resource_id]) => {
            // 查找 resource_id 对应的模型
            const { models, name, description } =
              resourcesData.find((r) => r.scene === scene)?.resource?.find((r) => r.id === resource_id) || {};
            return models
              ? getModelPermission(models, resource_id, 'allow_users', 'deny_users', name, description)
              : [];
          })
          .flat();
  } else {
    // 如果 user_permission 不为空，更新 flag
    setting.user_permission.forEach((item) => {
      item.flag = item.allow_users.length > 0;
    });
  }

  setting.collection_id = Array.isArray(setting.collection_id)
    ? setting.collection_id
    : setting.collection_id
    ? [setting.collection_id]
    : [];
  // 返回更新后的 setting
  return setting;
}
function handleResourceChange(values: ApiApp.AppResource['models']) {
  models.value = values;
}
</script>

<style scoped></style>
