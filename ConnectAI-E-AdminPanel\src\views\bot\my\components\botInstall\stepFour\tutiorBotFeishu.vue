<template>
  <div>
    <div class="block p-6 text-white rounded-lg bg-blue-500 min-h-520px">
      <h2 class="mb-1 text-2xl font-semibold">{{ $t('message.my.jqrcjzn') }}</h2>
      <a v-if="isLark" href="https://open.larksuite.com/app" target="_blank">
        <p class="mb-4 hover:underline text-xs font-bold">{{ $t('message.my.djqwfs') }}</p>
      </a>
      <a v-else href="https://open.feishu.cn/app" target="_blank">
        <p class="mb-4 hover:underline text-xs font-bold">{{ $t('message.my.djqwfs') }}</p>
      </a>
      <!-- List -->
      <div class="h-2" />
      <ul role="list" class="space-y-2 text-left">
        <li v-for="(item, index) in steps" :key="index" class="flex items-center space-x-2">
          <!--          <icon-akar-icons-light-bulb v-if="item.step === step" class="text-yellow-300 text-xl" />-->
          <icon-akar-icons-check v-if="item.step <= step" class="text-green-300 text-xl" />
          <icon-akar-icons-more-horizontal v-if="item.step > step" class="text-red-300 text-xl" />
          <span class="flex-1">{{ $t(item.content) }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { isLark } from '@/utils';

defineProps<{ step: number }>();

interface IStep {
  content: string;
  number: number;
  step: number;
  active?: boolean;
}

const steps = computed<IStep[]>(() => [
  {
    content: 'message.my.fs1',
    number: 1,
    step: 1
  },
  {
    content: 'message.my.fs2',
    number: 2,
    step: 1
  },
  {
    content: 'message.my.fs3',
    number: 3,
    step: 1
  },
  // 移除这个文案
  // {
  //   content: '打开权限管理-允许上传图片资源',
  //   number: 4,
  //   step: 1
  // },
  {
    content: 'message.my.fs5',
    number: 4,
    step: 1
  },
  {
    content: 'message.my.fs12',
    number: 5,
    step: 1
  },
  {
    content: 'message.my.fs13',
    number: 6,
    step: 1
  },
  {
    content: 'message.my.fs6',
    number: 7,
    step: 2
  },
  {
    content: 'message.my.fs7',
    number: 8,
    step: 2
  },
  {
    content: 'message.my.fs8',
    number: 9,
    step: 3
  },
  {
    content: 'message.my.fs9',
    number: 10,
    step: 3
  },
  {
    content: 'message.my.fs10',
    number: 11,
    step: 3
  },
  {
    content: 'message.my.fs11',
    number: 12,
    step: 3
  }
]);
</script>

<style scoped></style>
