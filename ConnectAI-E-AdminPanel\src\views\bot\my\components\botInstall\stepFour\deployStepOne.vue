<template>
  <div>
    <h1 class="mb-4 text-2xl font-extrabold leading-tight tracking-tight text-gray-900 sm:mb-6 dark:text-white">
      {{ $t('message.my.cjjqr') }}
    </h1>
    <p class="flex flex-justify-between mb-4 text-lg font-light text-gray-500 dark:text-gray-400">
      {{ t('message.my.sfwc') }}
    </p>
    <ul class="mb-6 space-y-4 sm:space-y-6">
      <li>
        <input
          id="developer"
          v-model="createFlag"
          type="radio"
          name="profession"
          value="later"
          class="hidden peer"
          @click="handleDeployPlugin"
        />
        <label
          for="developer"
          :data-id="app.id"
          :data-name="clientBot.name"
          :data-description="clientBot.description || t('message.my.ndfszs')"
          :data-logo="clientBot.logo || 'https://mpic.forkway.cn/cdn/logo/feishuapp.png'"
          :data-app-id="clientBot.app_id"
          :data-bot-id="clientBot.bot_id"
          :data-encrypt-key="clientBot.encript_key"
          :data-verification-token="clientBot.validation_token"
          :data-event-callback="clientBot.callback_url && clientBot.callback_url.event"
          :data-card-callback="clientBot.callback_url && clientBot.callback_url.card"
          :class="pulginInfo()"
          class="inline-flex items-center justify-center w-full p-5 text-gray-500 border-2 border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 peer-checked:border-blue-600 peer-checked:text-blue-600 bg-gray-50 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"
        >
          <svg
            class="w-6 h-6 mr-2"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            viewBox="0 0 24 24"
          >
            <path
              fill="currentColor"
              d="M11.5 19h1v-1.85l3.5-3.5V9H8v4.65l3.5 3.5V19Zm-2 2v-3L6 14.5V9q0-.825.588-1.413T8 7h1L8 8V3h2v4h4V3h2v5l-1-1h1q.825 0 1.413.588T18 9v5.5L14.5 18v3h-5Zm2.5-7Z"
            />
          </svg>
          <span v-if="clientBot.platform == 'feishu'" class="w-full">{{ t('message.my.plugin') }}</span>
          <span v-else-if="clientBot.platform == 'dingding'" class="w-full">{{ t('dingding测试') }}</span>
          <span v-else class="w-full">{{ t('message.my.xxyx') }}</span>
          <svg class="w-6 h-6 ml-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
              clip-rule="evenodd"
            ></path>
          </svg>
        </label>
      </li>
      <li>
        <input
          id="designer"
          v-model="createFlag"
          type="radio"
          name="profession"
          value="created"
          class="hidden peer"
          required
        />
        <label
          for="designer"
          class="inline-flex items-center justify-center w-full p-5 text-gray-500 border-2 border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 peer-checked:border-blue-600 peer-checked:text-blue-600 bg-gray-50 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"
        >
          <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z"
              clip-rule="evenodd"
            ></path>
          </svg>
          <span class="w-full">{{ t('message.my.zxqwkfz') }}</span>
          <svg class="w-6 h-6 ml-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
              clip-rule="evenodd"
            ></path>
          </svg>
        </label>
      </li>
    </ul>
    <button
      type="button"
      class="w-full px-5 py-2.5 sm:py-3.5 text-sm font-medium text-center text-white rounded-lg"
      :class="createFlag === 'created' ? btnClass.enabled : btnClass.disabled"
      @click="handleNextStep(2)"
    >
      {{ $t('message.my.xyb') }}
    </button>
    <p class="mt-4 text-sm font-light text-gray-500 dark:text-gray-400">
      {{ $t('message.my.yjwcjqr') }}
      <a
        class="cursor-pointer font-medium text-blue-600 hover:underline dark:text-blue-500"
        @click="handleFinalStep(3)"
        >{{ $t('message.my.djhqhd') }}</a
      >.
    </p>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, toRefs, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { PLUGIN_DOWNLOAD_URL } from '@/constants';
import { isLark as lark } from '@/utils';
import { t } from '@/locales';

const route = useRoute();
const createFlag = ref();
const btnClass = reactive({
  disabled: 'bg-blue-400 dark:bg-blue-500 cursor-not-allowed',
  enabled:
    'bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800'
});
const emit = defineEmits(['step-go']);
const status = ref(0);

const props = defineProps<{
  data: ApiApp.AppClientBot;
  app?: ApiApp.Application;
  reInstall?: boolean;
}>();

const { app, data: clientBot, reInstall } = toRefs(props);

const pulginInfo = () => {
  if (clientBot.value.platform == 'feishu') {
    return `connectai-auto-deploy${lark ? '-lark' : ''}`
  }else if (clientBot.value.platform == 'dingding'){
    return `connectai-auto-deploy-dingding`
  }else {
    return ''
  }
}

function handleNextStep(step: number) {
  if (createFlag.value === 'created') {
    emit('step-go', step);
  }
}

function handleFinalStep(step: number) {
  emit('step-go', step);
}

function handleDeployPlugin() {
  if (clientBot.value.platform === 'feishu') {
    open(PLUGIN_DOWNLOAD_URL);
  }
}
onMounted(() => {
  // 飞书默认选中later
  if (clientBot.value.platform === 'feishu') {
    createFlag.value = 'later';
  }
});
</script>

<style scoped></style>
