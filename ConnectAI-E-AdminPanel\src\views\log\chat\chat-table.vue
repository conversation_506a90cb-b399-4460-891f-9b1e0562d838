<template>
  <n-data-table scroll-x="1800" :columns="columns" :data="data" :bordered="false" /> 
  <div class="flex justify-end mt-4" ><n-pagination v-bind="paginationOptions" /></div>
</template>

<script setup lang="tsx">
import type { DataTableColumns, PaginationProps } from 'naive-ui';
import { NTag, NTooltip, NImage, NImageGroup, NSpace } from 'naive-ui';
import { t } from '@/locales';

const props = defineProps<{
  data: ApiSensitive.SensitiveTypes[];
  paginationOptions: PaginationProps;
}>();

const sourceDomain = ['aigcoss.dobestai.com', 'aigcoss.zhiqiteai.cn'];
function imageCDNUrl(image_url) {
  sourceDomain.forEach((domain) => {
    image_url = image_url.replace(domain, 'mpic.forkway.cn');
  });
  // 这里使用旧的cdn
  image_url = image_url.replace('cdn.aigcfun.com', 'pic.forkway.cn');

  return image_url + '/preview';
}

const columns: DataTableColumns<ApiSensitive.SensitiveTypes> = [
  {
    title: t('message.log.id'),
    key: 'id',
    width: 100,
    align: 'center',
    render(row, index) {
      return <div>{index + 1}</div>;
    }
  },
  {
    title: t('message.log.yymc'),
    key: 'app_name',
    width: 200,
    sorter: 'default',
    align: 'center'
  },
  {
    title: t('message.log.yhm'),
    key: 'username',
    resizable: true,
    sorter: 'default',
    align: 'center'
  },
  {
    title: t('message.log.wt'),
    key: 'content',
    resizable: true,
    width: 500,
    render({ content, message_type, extra, message_id }) {
      if (message_type === 'richText') {
        const codes =
          extra?.platform_content?.richText
            ?.filter((item) => item.type === 'picture')
            .map((item) => item.downloadCode) || [];
        const imageUrls = codes.map((code) => {
          const prefix =
            import.meta.env.PROD === false ? '/api/api/dingding/image/message' : '/api/dingding/image/message';
          return `${prefix}?message_id=${message_id}&download_code=${encodeURIComponent(code)}`;
        });
        const text = extra?.platform_content?.richText.find((item) => item.type === 'text')?.text;

        if (imageUrls.length > 0) {
          return (
            <div>
              <NImageGroup>
                <NSpace>
                  {imageUrls.map((url) => {
                    const src = url;
                    return <NImage width="100" src={src} alt={content || '图片'} />;
                  })}
                </NSpace>
              </NImageGroup>
              <div>{text}</div>
            </div>
          );
        }
      }

      if (message_type === 'post') {
        const imageKeys =
          extra?.platform_content?.content
            ?.filter((item) => item?.[0]?.tag === 'img')
            .map((item) => item?.[0]?.image_key) || [];
        const text = extra?.platform_content?.content
          ?.filter((item) => item?.[0]?.tag === 'text')
          .map((item) => item?.[0]?.text)
          .join('\n');

        const imageUrls = imageKeys.map((imageKey) => {
          const prefix = import.meta.env.PROD === false ? '/api/api/feishu/image/message' : '/api/feishu/image/message';
          return `${prefix}?message_id=${message_id}&image_key=${imageKey}`;
        });

        if (imageUrls.length > 0) {
          return (
            <div>
              <NImageGroup>
                <NSpace>
                  {imageUrls.map((url) => {
                    const src = url;
                    return <NImage width="100" src={src} alt={content || '图片'} />;
                  })}
                </NSpace>
              </NImageGroup>
              <div>{text}</div>
            </div>
          );
        }
      }

      return (
        <NTooltip width={400}>
          {{
            trigger: () => <span class="line-clamp-2 cursor-pointer">{content}</span>,
            default: () => <div>{content}</div>
          }}
        </NTooltip>
      );
    }
  },
  {
    title: t('message.log.hd'),
    key: 'result',
    resizable: true,
    width: 300,
    render({ result, content }) {
      const resultContent = result?.content || '';
      const imageUrls = result?.additional_kwargs?.differentialImageRspList;
      // console.log(result);
      if (imageUrls?.length > 0) {
        return (
          <NImageGroup>
            <NSpace>
              {imageUrls.map((item) => {
                const src = imageCDNUrl(item?.image_url);
                const fallbackSrc = src.replace('mpic', 'pic');
                return (
                  <NImage
                    width="100"
                    src={src}
                    fallback-src={fallbackSrc}
                    alt={content || '图片'}
                    onError={(e) => {
                      e.target.src = fallbackSrc;
                    }}
                  />
                );
              })}
            </NSpace>
          </NImageGroup>
        );
      }

      // 直接使用 image_url 地址
      if (result?.additional_kwargs?.task_result?.image_url) {
        const src = result.additional_kwargs.task_result.image_url
        return (
          <NImage
            width="100"
            src={src}
            alt={content || '图片'}
          />
        );
      }


      if (result?.additional_kwargs?.imageURL) {
        // 使用自己的CDN地址，并且增加预览配置
        const src = imageCDNUrl(result.additional_kwargs.imageURL);
        // falback proxy.aionekey.shop 要去除末尾的preview
        const fallbackSrc = src.replace('mpic.forkway.cn', 'proxy.aionekey.shop').replace(/\/preview$/, '');
        return (
          <NImage
            width="100"
            src={src}
            fallback-src={fallbackSrc}
            alt={content || '图片'}
            onError={(e: Event) => {
              e.target.src = fallbackSrc;
            }}
          />
        );
      }

      if (result?.additional_kwargs?.imageUrl && result?.additional_kwargs?.action === 'DESCRIBE') {
        // 图生文字
        const text = result?.additional_kwargs?.prompt;
        return (
          <NTooltip width={400}>
            {{
              trigger: () => <div class="line-clamp-2 cursor-pointer">{text}</div>,
              default: () => <div>{text}</div>
            }}
          </NTooltip>
        );
      }

      if (result?.additional_kwargs?.imageUrl && result?.additional_kwargs?.action !== 'DESCRIBE') {
        // 图生图片
        // 使用自己的CDN地址，并且增加预览配置
        const src = imageCDNUrl(result.additional_kwargs.imageUrl);
        // falback proxy.aionekey.shop 要去除末尾的preview
        const fallbackSrc = src.replace('mpic.forkway.cn', 'proxy.aionekey.shop').replace(/\/preview$/, '');
        return (
          <NImage
            width="100"
            src={src}
            fallback-src={fallbackSrc}
            alt={content || '图片'}
            onError={(e) => {
              e.target.src = fallbackSrc;
            }}
          />
        );
      }

      return (
        <NTooltip width={400}>
          {{
            trigger: () => <span class="line-clamp-2 cursor-pointer">{resultContent}</span>,
            default: () => <div>{resultContent}</div>
          }}
        </NTooltip>
      );
    }
  },
  {
    title: t('message.log.fxtw'),
    key: 'is_sensitive',
    resizable: true,
    sorter: 'default',
    align: 'center',
    width: 100,
    render({ sensitive }) {
      if (sensitive) {
        return <span>{t('message.log.yes')}</span>;
      }
      return <span>{t('message.log.no')}</span>;
    }
  },
  {
    title: t('message.log.fx'),
    key: 'sensitive.name',
    resizable: true,
    align: 'center',
    render({ sensitive = {} }) {
      return (
        <div class="i-flex-center gap-2">
          {((sensitive && sensitive.name) || []).map((str) => {
            return <NTag>{str}</NTag>;
          })}
        </div>
      );
    }
  },
  {
    title: t('message.log.cjsj'),
    key: 'created',
    resizable: true,
    align: 'center'
  }
];

</script>
