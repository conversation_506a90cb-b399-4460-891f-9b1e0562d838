import logging
import hashlib
from base import *
from uuid import uuid4
from core.mysql import get_engine_by_name, get_session_by_name
from core.base_model import MysqlModel
from core.schema import (
    ObjID, Base,
    TenantWithKey, TenantAdmin, Account,
    ResourceCategory, Resource, Model,
    Application, ApplicationImplementation,
    ApplicationSupportResource, ApplicationCategory,
    ApplicationSupportBot,
    AppInstance, BotInstance, AppInstanceBotInstance, Policy,
    PromptCategory, Prompt, Sensitive,
    Product,
    Bot,
    text
)
from core.utils import hash_password
from tornado.ioloop import IOLoop
from os.path import abspath, dirname
from settings.constant import (
    ModelCategory,
    VALIDATION_TOKEN, ENCRIPT_KEY
)


class InitData(MysqlModel):

    async def try_insert_record(self, Schema, uniq_key, id='', **data):
        if not isinstance(uniq_key, list):
            uniq_key = [uniq_key]
        filters = [getattr(Schema, k) == data.get(k, id) for k in uniq_key]
        record_id = self.session.query(Schema.id).filter(*filters).limit(1).scalar()
        if record_id:
            self.session.begin_nested()
            self.session.query(Schema).filter(Schema.id == record_id).update(data, synchronize_session=False)
            self.session.commit()
            return record_id
        record_id = id or ObjID.new_id()
        self.session.begin_nested()
        self.session.add(Schema(
            id=record_id,
            **data,
        ))
        self.session.commit()
        return record_id

    async def init_resource(self):
        resource_category = [
            {'name': 'LLM', 'description': ''},
            {'name': '绘画', 'description': ''},
            {'name': '音视频', 'description': ''},
            {'name': '视觉', 'description': ''},
        ]
        resource_category_ids = [await self.try_insert_record(ResourceCategory, 'name', **i) for i in resource_category]
        logging.info("resource_category_ids %r", resource_category_ids)
        top = {
            "label": "还没有相关资源？不知道如何配置？点击👉",
            "url": "https://connect-ai.feishu.cn/docx/CoBydipXSoQoQ7xmTBVcsZECnTf",
        }
        api_base = {
            "name": "api_base",
            "label": "服务域名",
            "required": True,
            "placeholder": "请输入URL",
            "value": "",
            "size": "full",
        }
        api_key = {
            "name": "api_key",
            "label": "API Key",
            "required": True,
            "placeholder": "请输入API Key",
            "value": "",
            "size": "full",
        }
        group_id = {
            "name": "group_id",
            "label": "Group ID",
            "required": False,
            "placeholder": "请输入Group ID",
            "value": "",
            "size": "full",
        }
        app_id = {
            "name": "app_id",
            "label": "AppID",
            "required": False,
            "placeholder": "请输入AppID",
            "value": "",
            "size": "half",
        }
        region_id = {
            "name": "region_id",
            "label": "Region ID",
            "required": False,
            "placeholder": "请输入Region ID",
            "value": "",
            "size": "half",
        }
        access_key_id = {
            "name": "access_key_id",
            "label": "Access Key",
            "required": False,
            "placeholder": "请输入Access Key",
            "value": "",
            "size": "half",
        }
        secret_id = {
            "name": "secret_id",
            "label": "Secret ID",
            "required": False,
            "placeholder": "请输入Secret ID",
            "value": "",
            "size": "half",
        }
        secret_key = {
            "name": "secret_key",
            "label": "Secret Key",
            "required": False,
            "placeholder": "请输入Secret Key",
            "value": "",
            "size": "half",
        }
        full_secret_key = {
            "name": "secret_key",
            "label": "Secret Key",
            "required": False,
            "placeholder": "请输入Secret Key",
            "value": "",
            "size": "full",
        }
        def model(models, deploy_name=False, tip=''):
            return {
                "name": "model",
                "label": "支持模型",
                "required": True,
                "size": "full",
                # "tip": tip or "请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用会报错",
                "options": [{'label': m, 'value': m} for m in models],
                "value": "",
                "deploy_name": deploy_name,
            }
        def tip(label):
            return {
                'label': label,
                'name': 'tip',
                'size': 'full',
            }
        resource = [
            {
                'category_id': resource_category_ids[0],
                'name': 'OpenAI', 'description': '生成式 AI 大模型，能够应用于各种用例，支持GPT-3.5、GPT-4 8K、GPT-4 32K模型',
                'icon': '', 'provider': 'OpenAI', 'sorted': 12,
                'config': {
                    "title": "OpenAI", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['gpt-3.5-turbo', 'gpt-3.5-turbo-16k', 'gpt-4', 'gpt-4-1106-preview', 'gpt-4-32k', 'dall-e-3', 'tts-1', 'tts-1-hd', 'whisper-1', 'gpt-4-vision-preview']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错')
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'Azure', 'description': '具备OpenAI的所有能力，例如编写帮助、代码生成和数据推理，支持GPT-3.5、GPT-4 8K、GPT-4 32K模型，通过内置负责任的 AI 检测和缓解有害使用，并访问企业级 Azure 安全性。',
                'icon': '', 'provider': '微软云', 'sorted': 11,
                'config': {
                    "title": "Azure", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['gpt-3.5-turbo', 'gpt-3.5-turbo-16k', 'gpt-35-turbo-1106', 'gpt-4', 'gpt-4-1106-preview', 'gpt-4-32k'], True),
                        tip('1、若您使用的是Azure官方资源，请务必填写Deployname，且保持命名与Azure 后台设置的保持一致'),
                        tip('2、请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': '文心一言', 'description': '作为扎根于中国市场的大语言模型，在5500亿事实知识图谱的赋能下，文心一言具备中文领域更先进的自然语言处理能力，在中文语言和中国文化上有更好的表现。',
                'icon': '', 'provider': '百度千帆', 'sorted': 4,
                'config': {
                    "title": "文心一言", "top": top,
                    "form": [
                        api_base, api_key,
                        app_id, secret_key,
                        tip('若您使用文心一言官方资源，请务必填写AppID、Secret Key'),
                        model(['ERNIE-Bot', 'ERNIE-Bot-turbo', 'BLOOMZ-7B', 'ERNIE-Bot 4.0']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[1],
                'name': 'Midjourney', 'description': '通过自然语言关键词描述，就能通过AI算法生成相应的图片，支持快速模式、慢速模式，支持最新的V5版本',
                'icon': '', 'provider': 'Midjourney', 'sorted': 3,
                'config': {
                    "title": "Midjourney", "top": top,
                    "form": [api_base, api_key, model(['midjourney_v5'])]
                }
            },
            {
                'category_id': resource_category_ids[1],
                'name': 'StableDiffusion', 'description': '待上架',
                'icon': '', 'provider': 'StableDiffusion', 'sorted': 10, 'status': -1,
                'config': {
                    "title": "StableDiffusion", "top": top,
                    "form": [api_base, api_key, model([])],
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'Claude', 'description': 'Claude是一个由Anthropic开发的大语言模型，性能媲美GPT-3.5，最大特点是支持100K的超长上下文',
                'icon': '', 'provider': 'Claude', 'sorted': 15,
                'config': {
                    "title": "Claude", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['claude-1', 'claude-instant-1', 'claude-2']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'RWKV', 'description': 'RWKV是一个具有Transformer水平的LLM性能的RNN模型。它可以像GPT一样直接进行训练，具有出色的性能、快速推理、快速训练、”无限”的上下文长度和超低费率。',
                'icon': '', 'provider': 'RWKV.COM', 'sorted': 6,
                'config': {
                    "title": "RWKV", "top": top,
                    "form": [api_base, api_key, model(['RWKV-4-World-7B'])]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'MiniMax', 'description': '基于MiniMax端到端自研多模态大语言模型，以自然语言交互的形式帮助企业用户或企业开发者提高文本相关的生产效率。',
                'icon': '', 'provider': 'MiniMax', 'sorted': 7,
                'config': {
                    "title": "MiniMax", "top": top,
                    "form": [
                        api_base, api_key,
                        group_id,
                        tip('若您使用MiniMax官方资源，请务必填写GroupID'),
                        model(['abab5.5-chat', 'abab5-chat']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': '星火认知大模型', 'description': '讯飞星火认知大模型，是以中文为核心的新一代认知智能大模型。',
                'icon': '', 'provider': '科大讯飞', 'sorted': 8,
                'config': {
                    "title": "星火认知大模型", "top": top,
                    "form": [
                        api_base, api_key, app_id, secret_key,
                        tip('若您使用讯飞星火官方资源，请务必填写AppID、Secret Key'),
                        # model(['spark1.1-chat', 'spark2.1-chat', 'spark3.1-chat']),
                        model(['spark3.1-chat', 'spark2.1-chat']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'ChatGLM', 'description': 'ChatGLM是清华大学知识工程和数据挖掘小组发布的一个开源的对话机器人。',
                'icon': '', 'provider': '清华大学', 'sorted': 9,
                'config': {
                    "title": "ChatGLM", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['chatglm_lite', 'chatglm_std']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[1],
                'name': 'Stable Diffusion', 'description': '目前最好用的开源AI绘图模型，可自由定制专属lora。',
                'icon': '', 'provider': '多比特', 'sorted': 14,
                'config': {
                    "title": "Stable Diffusion", "top": top,
                    "form": [
                        api_base, api_key, app_id, secret_key,
                        # models v2
                        model(['CuteYukiMix(特化可爱风格adorable style）', 'ouka_gufeng', 'Samaritan 3d Cartoon', 'Deliberate', 'majicMIX realistic 麦橘写实', 'DreamShaper']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': '360智脑', 'description': '首创大模型安全评估体系，近20年互联网内容能力积累审核',
                'icon': '', 'provider': '360', 'sorted': 10,
                'config': {
                    "title": "360智脑", "top": top,
                    "form": [
                        api_base, api_key,
                        # models v2
                        model(['360GPT_S2_V9', '360GPT_S2_V9.4']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'BibiGPT', 'description': 'ChatGPT AI 音视频一键总结，轻松学习哔哩哔哩丨YouTube丨播客丨小红书丨抖音等内容。BibiGPT 助力于成为最好的 AI 学习助理，支持免费试用！',
                'icon': '', 'provider': 'bibigpt.co', 'sorted': 16,
                'config': {
                    "title": "BibiGPT AI音视频助理", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['BibiGPT']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': '百川大模型', 'description': '百川大模型-汇聚世界知识 创作妙笔生花-百川智能',
                'icon': '', 'provider': '百川智能', 'sorted': 5,
                'config': {
                    "title": "百川大模型", "top": top,
                    "form": [
                        api_base, api_key,
                        full_secret_key,
                        tip('若您使用百川官方资源，请务必填写Secret Key'),
                        model(['Baichuan2-53B']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': '商汤日日新', 'description': '商量商量，都能解决',
                'icon': '', 'provider': '商汤', 'sorted': 5,
                'config': {
                    "title": "商汤日日新", "top": top,
                    "form": [
                        api_base, api_key,
                        full_secret_key,
                        model(['nova-ptc-xl-v1','nova-ptc-xs-v1']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[1],
                'name': 'Pixian.AI', 'description': '基于AI算法快速去除图像的背景',
                'icon': '', 'provider': 'pixian', 'sorted': 27,
                'config': {
                    "title": "Pixian.AI", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['pixian-v2']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[1],
                'name': 'Clipdrop', 'description': '仅需几秒，即可实现图像的高级提升、噪点清除和质量增强！',
                'icon': '', 'provider': 'Clipdrop', 'sorted': 28,
                'config': {
                    "title": "Clipdrop", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['image-upscaling-v1', 'remove-text-v1']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[2],
                'name': 'Moises.AI', 'description': '基于AI算法快速分离人声和背景',
                'icon': '', 'provider': 'moises', 'sorted': 27,
                'config': {
                    "title": "Moises.AI", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['moises']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'Moonshot', 'description': '💥 突破20万上下文窗口的对话大模型，来自国产大模型明星团队-月之暗面，帮你看更大的世界',
                'icon': '', 'provider': 'Moonshot AI', 'sorted': 31,
                'config': {
                    "title": "Moonshot", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['moonshot-v1-8k']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': '通义千问', 'description': '阿里云研发的大规模视觉语言模型，支持中文多模态对话及多图对话',
                'icon': '', 'provider': '阿里云', 'sorted': 32,
                'config': {
                    "title": "通义千问", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['qwen-turbo', 'qwen-plus']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[2],
                'name': 'Vectorizer.AI', 'description': '基于AI算法将位图转换为高质量的矢量图',
                'icon': '', 'provider': 'Vectorize AI', 'sorted': 33,
                'config': {
                    "title": "Vectorizer.AI", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['vectorizer']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': '紫东太初', 'description': '中科院自动化所和武汉人工智能研究院联合推出新一代大模型',
                'icon': '', 'provider': '中科院自动化所', 'sorted': 34,
                'config': {
                    "title": "紫东太初", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['taichu_llm_8b']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'AliyunOpenAPI', 'description': '阿里云OpenAPI',
                'icon': '', 'provider': 'Aliyun', 'sorted': 32,
                'config': {
                    "title": "阿里云OpenAPI", "top": top,
                    "form": [
                        api_base, api_key,
                        # 下面三个是用户使用自己的阿里云帐号
                        region_id, access_key_id, full_secret_key,
                        model(['alimt']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[1],
                'name': 'ElevenLabs', 'description': '最先进的文本到语音模型，以任何声音、风格和语言生成高质量的口语音频。',
                'icon': '', 'provider': 'elevenlabs', 'sorted': 30,
                'config': {
                    "title": "ElevenLabs", "top": top,
                    "form": [
                        api_base, api_key,
                        model(['eleven_monolingual_v2']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'GPTs', 'description': 'GPTs',
                'icon': '', 'provider': 'OpenAI', 'sorted': 32,
                'config': {
                    "title": "GPTs", "top": top,
                    "form": [
                        api_base, api_key,
                        # model(['gpt-4-gizmo-g-HEChZ7eza']),
                        model(['gpt-4-all', 'gpt-4-gizmo-g-tBei7TkK0', 'gpt-4-gizmo-g-n7Rs0IK86', 'gpt-4-gizmo-g-INlwuHdxU', 'gpt-4-gizmo-g-B8Jiuj0Dp', 'gpt-4-gizmo-g-uPFa8qH8y']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'Hunyuan', 'description': 'Tencent Hunyuan',
                'icon': '', 'provider': 'Tencent', 'sorted': 33,
                'config': {
                    "title": "腾讯混元", "top": top,
                    "form": [
                        api_base, api_key,
                        app_id, secret_id, secret_key,
                        model(['hyllm']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
            {
                'category_id': resource_category_ids[0],
                'name': 'Gemini', 'description': 'Google Gemini',
                'icon': '', 'provider': 'Google', 'sorted': 34,
                'config': {
                    "title": "Gemini", "top": top,
                    "form": [
                        api_base, api_key,
                        # model(['gemini-pro', 'gemini-pro-vision']),
                        model(['gemini-pro']),
                        tip('请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错'),
                    ]
                }
            },
        ]

        resource_ids = [await self.try_insert_record(Resource, 'name', **i) for i in resource]
        logging.info("resource_ids %r", resource_ids)
        models = [
            # Azure
            {'resource_id': resource_ids[1], 'name': 'gpt-3.5-turbo', 'price': 1},
            {'resource_id': resource_ids[1], 'name': 'gpt-3.5-turbo-16k', 'price': 1},
            {'resource_id': resource_ids[1], 'name': 'gpt-35-turbo-1106', 'price': 1},
            {'resource_id': resource_ids[1], 'name': 'gpt-4', 'price': 10},
            {'resource_id': resource_ids[1], 'name': 'gpt-4-1106-preview', 'price': 1},
            {'resource_id': resource_ids[1], 'name': 'gpt-4-32k', 'price': 100},

            # OpenAI
            {'resource_id': resource_ids[0], 'name': 'gpt-3.5-turbo', 'price': 1},
            {'resource_id': resource_ids[0], 'name': 'gpt-3.5-turbo-16k', 'price': 1},
            {'resource_id': resource_ids[0], 'name': 'gpt-4', 'price': 10},
            {'resource_id': resource_ids[0], 'name': 'gpt-4-1106-preview', 'price': 90},
            {'resource_id': resource_ids[0], 'name': 'gpt-4-32k', 'price': 100},
            {'resource_id': resource_ids[0], 'name': 'dall-e-3', 'price': 500, 'category': ModelCategory.Draw.value},
            {'resource_id': resource_ids[0], 'name': 'tts-1', 'price': 1000, 'category': ModelCategory.TTS.value},
            {'resource_id': resource_ids[0], 'name': 'tts-1-hd', 'price': 1100, 'category': ModelCategory.TTS.value},
            {'resource_id': resource_ids[0], 'name': 'gpt-4-vision-preview', 'price': 90, 'category': ModelCategory.Vision.value},
            {'resource_id': resource_ids[0], 'name': 'whisper-1', 'price': 500, 'category': ModelCategory.STT.value},

            # 文心
            {'resource_id': resource_ids[2], 'name': 'ERNIE-Bot', 'price': 1},
            {'resource_id': resource_ids[2], 'name': 'ERNIE-Bot-turbo', 'price': 1},
            {'resource_id': resource_ids[2], 'name': 'BLOOMZ-7B', 'price': 1},
            {'resource_id': resource_ids[2], 'name': 'ERNIE-Bot 4.0', 'price': 1},
            # midjourney
            {'resource_id': resource_ids[3], 'name': 'midjourney_v5', 'price': 1, 'category': ModelCategory.Draw.value},
            # claude
            {'resource_id': resource_ids[5], 'name': 'claude-1', 'price': 1},
            {'resource_id': resource_ids[5], 'name': 'claude-instant-1', 'price': 1},
            {'resource_id': resource_ids[5], 'name': 'claude-2', 'price': 1},
            # rwkv
            {'resource_id': resource_ids[6], 'name': 'RWKV-4-World-7B', 'price': 1},
            # minimax
            {'resource_id': resource_ids[7], 'name': 'abab5.5-chat', 'price': 1},
            {'resource_id': resource_ids[7], 'name': 'abab5-chat', 'price': 1},
            # 星火认知大模型
            {'resource_id': resource_ids[8], 'name': 'spark1.1-chat', 'price': 1},
            {'resource_id': resource_ids[8], 'name': 'spark2.1-chat', 'price': 1},
            {'resource_id': resource_ids[8], 'name': 'spark3.1-chat', 'price': 1},
            # chatglm-6b
            {'resource_id': resource_ids[9], 'name': 'chatglm_lite', 'price': 1},
            {'resource_id': resource_ids[9], 'name': 'chatglm_std', 'price': 1},

            # sd models v2
            {'resource_id': resource_ids[10], 'name': 'CuteYukiMix(特化可爱风格adorable style）', 'price': 1, 'category': ModelCategory.Draw.value},
            {'resource_id': resource_ids[10], 'name': 'ouka_gufeng', 'price': 1, 'category': ModelCategory.Draw.value},
            {'resource_id': resource_ids[10], 'name': 'Samaritan 3d Cartoon', 'price': 1, 'category': ModelCategory.Draw.value},
            {'resource_id': resource_ids[10], 'name': 'Deliberate', 'price': 1, 'category': ModelCategory.Draw.value},
            {'resource_id': resource_ids[10], 'name': 'majicMIX realistic 麦橘写实', 'price': 1, 'category': ModelCategory.Draw.value},
            {'resource_id': resource_ids[10], 'name': 'DreamShaper', 'price': 1, 'category': ModelCategory.Draw.value},
            # 360智脑
            {'resource_id': resource_ids[11], 'name': '360GPT_S2_V9', 'price': 1},
            {'resource_id': resource_ids[11], 'name': '360GPT_S2_V9.4', 'price': 1},
            # bibigpt
            {'resource_id': resource_ids[12], 'name': 'BibiGPT', 'price': 1, 'category': ModelCategory.Video.value},
            # 百川
            {'resource_id': resource_ids[13], 'name': 'Baichuan2-53B', 'price': 1},
            #商汤日日新
            {'resource_id': resource_ids[14], 'name': 'nova-ptc-xl-v1', 'price': 1},
            {'resource_id': resource_ids[14], 'name': 'nova-ptc-xs-v1', 'price': 1},
            # BgRemove
            {'resource_id': resource_ids[15], 'name': 'pixian-v2', 'price': 1, 'category': ModelCategory.Image.value},
            # upscaler  removetext
            {'resource_id': resource_ids[16], 'name': 'image-upscaling-v1', 'price': 1, 'category': ModelCategory.Image.value},
            {'resource_id': resource_ids[16], 'name': 'remove-text-v1', 'price': 1, 'category': ModelCategory.Image.value},
            # RemoveVc
            {'resource_id': resource_ids[17], 'name': 'moises', 'price': 1, 'category': ModelCategory.Audio.value},
            # Moonshot
            {'resource_id': resource_ids[18], 'name': 'moonshot-v1-8k', 'price': 1},
            # 通义千问
            {'resource_id': resource_ids[19], 'name': 'qwen-turbo',  'price': 1},
            {'resource_id': resource_ids[19], 'name': 'qwen-plus', 'price': 1},
            # Bittosvg
            {'resource_id': resource_ids[20], 'name': 'vectorizer', 'price': 1, 'category': ModelCategory.Image.value},
            # 紫东太初
            {'resource_id': resource_ids[21], 'name': 'taichu_llm_8b', 'price': 1},
            {'resource_id': resource_ids[21], 'name': 'taichu_vqa_10b', 'price': 1},
            # AliyunOpenAPI
            {'resource_id': resource_ids[22], 'name': 'alimt', 'price': 1, 'category': ModelCategory.Doc.value},

            # elevenlabs
            {'resource_id': resource_ids[23], 'name': 'eleven_monolingual_v1', 'price': 1, 'category': ModelCategory.TTS.value},
            {'resource_id': resource_ids[23], 'name': 'eleven_monolingual_v2', 'price': 1, 'category': ModelCategory.TTS.value},

            # GPTs
            {'resource_id': resource_ids[24], 'name': 'gpt-4-all', 'price': 1000},  # 排在前面
            {'resource_id': resource_ids[24], 'name': 'gpt-4-gizmo-g-HEChZ7eza', 'category': ModelCategory.LLM.value, 'description': 'BibiGPT', 'price': 1100},
            # not found {'resource_id': resource_ids[24], 'name': 'gpt-4-gizmo-g-dRmGnuzWV', 'category': ModelCategory.LLM.value, 'description': '投资人筛选器', 'price': 1100},
            {'resource_id': resource_ids[24], 'name': 'gpt-4-gizmo-g-tBei7TkK0', 'category': ModelCategory.LLM.value, 'description': 'Spotify Explorer GPT', 'price': 1100},
            {'resource_id': resource_ids[24], 'name': 'gpt-4-gizmo-g-n7Rs0IK86', 'category': ModelCategory.LLM.value, 'description': 'Grimoire', 'price': 1100},
            {'resource_id': resource_ids[24], 'name': 'gpt-4-gizmo-g-INlwuHdxU', 'category': ModelCategory.LLM.value, 'description': 'Screenplay GPT', 'price': 1100},
            {'resource_id': resource_ids[24], 'name': 'gpt-4-gizmo-g-B8Jiuj0Dp', 'category': ModelCategory.LLM.value, 'description': 'Drawn to Style', 'price': 1100},
            {'resource_id': resource_ids[24], 'name': 'gpt-4-gizmo-g-uPFa8qH8y', 'category': ModelCategory.LLM.value, 'description': 'Recipe Snap', 'price': 1100},

            # Tencent Hunyuan
            {'resource_id': resource_ids[25], 'name': 'hyllm', 'category': ModelCategory.LLM.value, 'price': 1},

            # Gemini
            {'resource_id': resource_ids[26], 'name': 'gemini-pro', 'price': 1},
            {'resource_id': resource_ids[26], 'name': 'gemini-pro-vision', 'price': 1},
        ]
        # model.category 这里默认为LLM
        for i in models:
            i['category'] = i.get('category', ModelCategory.LLM.value)
        model_ids = [await self.try_insert_record(Model, ['name', 'resource_id'], **i) for i in models]
        logging.info("model_ids %r", model_ids)
        return resource_category_ids, resource_ids, model_ids

    async def init_bot(self):
        bots = [
            {'platform': 'feishu', 'name': '飞书机器人', 'description': '', 'component': 'IM-Bot'},
            {'platform': 'dingding', 'name': '钉钉机器人', 'description': '', 'component': 'IM-Bot'},
            {'platform': 'wxwork', 'name': '企微第三方应用', 'description': '第三方机器人', 'component': 'IM-Bot'},
            {'platform': 'wework', 'name': '企微自建应用', 'description': '自建机器人', 'component': 'IM-Bot'},
            {'platform': 'messenger', 'name': '客服机器人', 'description': '客服机器人', 'component': 'IM-Bot'},
        ]
        bot_ids = [await self.try_insert_record(Bot, 'platform', **bot) for bot in bots]
        logging.info("bot_ids %r", bot_ids)

    async def init_user(self):
        tenants = [{'name': 'ai-feishu', 'apikey': 'connectai-647f3ef47033660001217897'}]
        tenant_ids = [await self.try_insert_record(TenantWithKey, 'name', **tenant) for tenant in tenants]
        users = [
            {'name': 'ai-feishu', 'email': '<EMAIL>', 'tenant_id': tenant_ids[0], 'passwd': hash_password('123qwe')},
        ]
        user_ids = [await self.try_insert_record(Account, 'email', **user) for user in users]
        logging.info("user_ids %r", user_ids)
        tenant_admin = [{ 'tenant_id': tenant_ids[0], 'user_id': user_ids[0], 'id': user_ids[0] }]
        admin_ids = [await self.try_insert_record(TenantAdmin, 'user_id', **admin) for admin in tenant_admin]
        logging.info("admin_ids %r", admin_ids)
        return tenant_ids, user_ids, admin_ids

    async def init_prompt(self, tenant_id, user_id):
        categories = [
            {'tenant_id': tenant_id, 'user_id': user_id, 'name': '文案撰写'},
            {'tenant_id': tenant_id, 'user_id': user_id, 'name': '生活助手'},
            {'tenant_id': tenant_id, 'user_id': user_id, 'name': '代码专家'},
            {'tenant_id': tenant_id, 'user_id': user_id, 'name': '角色扮演'},
            {'tenant_id': tenant_id, 'user_id': user_id, 'name': 'AI绘图'},
        ]
        category_ids = [await self.try_insert_record(PromptCategory, ['tenant_id', 'user_id', 'name'], **i) for i in categories]
        logging.info("category_ids %r", category_ids)
        prompts = [
            {
                'tenant_id': tenant_id, 'user_id': user_id, 'category_id': category_ids[0],
                'title': '公文写作', 'description': '各种材料汇报专业撰写，让你高效完成工作任务',
                'content': '你是某机关单位办公室秘书，你熟悉各类公文写作格式，你喜欢撰写文字材料，请你文采过人地，条理清晰地跟我对话',
                'example': '你好，我是某某某，我想要你帮我写一份公文，内容是：团结一致，共同抗击疫情，全力以赴，共克时艰。'
            },
        ]
        prompt_ids = [await self.try_insert_record(Prompt, ['tenant_id', 'user_id', 'category_id', 'title'], **i) for i in prompts]
        logging.info("prompt_ids %r", prompt_ids)

    async def init_sensitive(self, tenant_ids, user_ids):
        sensitives = [
            {'id': '649e4c9aa98e95f1639df534', 'tenant_id': tenant_ids[0], 'user_id': user_ids[0], 'category': '公司级别', 'name': ["百度", "腾讯"]},
        ]
        sensitive_ids = [await self.try_insert_record(Sensitive, 'id', **i) for i in sensitives]
        logging.info("sensitive_ids %r", sensitive_ids)

    async def init_product(self):
        person_items = [
            (True, 'AI应用接入：2'),
            (True, '应用提示词：100'),
            (True, '通用AI机器人'),
            (False, 'AI交互日志'),
            (False, '内部知识库'),
            (False, '行业场景机器人'),
            (False, '低代码AI工作流'),
            (False, 'AI插件生态'),
            (False, '一对一服务支持'),
            (False, '开源模型自部署'),
            (False, 'AI场景定制'),
        ]
        enterprise_items = [
            (True, 'AI应用接入：20'),
            (True, '应用提示词：1k'),
            (True, '通用AI机器人'),
            (True, 'AI交互日志：10w'),
            (True, '内部知识库：30G'),
            (True, '行业场景机器人'),
            (True, '低代码AI工作流'),
            (True, 'AI插件生态'),
            (True, '一对一服务支持'),
            (False, '开源模型自部署'),
            (False, 'AI场景定制'),
        ]
        privatization_items = [
            (True, 'AI应用接入：无限制'),
            (True, '应用提示词：无限制'),
            (True, '通用AI机器人'),
            (True, 'AI交互日志：无限制'),
            (True, '内部知识库：无限制'),
            (True, '行业场景机器人'),
            (True, '低代码AI工作流'),
            (True, 'AI插件生态'),
            (True, '一对一服务支持'),
            (True, '开源模型自部署'),
            (True, 'AI场景定制'),
        ]
        # v1.9 product items
        preview_items = [
            (True, '内部成员', '10人'),
            (True, 'token 赠送', '20万'),
            (True, '主流 AI 模型支持', '10+'),
            (True, '支持接入自有 AI 资源'),
            (False, '开源 AI 模型私有化部署'),
            (True, 'AI 应用接入', '20个'),
            (True, 'AI 应用提示词', '1000+'),
            (True, 'AI 知识库容量', '30G'),
            (True, '标准 AI 行业场景应用'),
            (False, '定制 AI 行业场景应用'),
            (True, 'AI 交互日志', '10万条'),
            (True, 'AI 应用及模型权限管控'),
            (False, 'AI 插件能力生态'),
            (False, '低代码 AI 工作流工具'),
            (True, '一对一服务支持'),
        ]
        standard_3m_items = [
            (True, '内部成员', '10人'),
            (True, 'token 赠送', '1000万'),
            (True, '主流 AI 模型支持', '10+'),
            (True, '支持接入自有 AI 资源'),
            (False, '开源 AI 模型私有化部署'),
            (True, 'AI 应用接入', '20个'),
            (True, 'AI 应用提示词', '1000+'),
            (True, 'AI 知识库容量', '30G'),
            (True, '标准 AI 行业场景应用'),
            (False, '定制 AI 行业场景应用'),
            (True, 'AI 交互日志', '10万条'),
            (True, 'AI 应用及模型权限管控'),
            (False, 'AI 插件能力生态'),
            (False, '低代码 AI 工作流工具'),
            (True, '一对一服务支持'),
        ]
        standard_1y_items = [
            (True, '内部成员', '10人'),
            (True, 'token 赠送', '3200万'),
            (True, '主流 AI 模型支持', '10+'),
            (True, '支持接入自有 AI 资源'),
            (False, '开源 AI 模型私有化部署'),
            (True, 'AI 应用接入', '20个'),
            (True, 'AI 应用提示词', '1000+'),
            (True, 'AI 知识库容量', '30G'),
            (True, '标准 AI 行业场景应用'),
            (False, '定制 AI 行业场景应用'),
            (True, 'AI 交互日志', '10万条'),
            (True, 'AI 应用及模型权限管控'),
            (False, 'AI 插件能力生态'),
            (False, '低代码 AI 工作流工具'),
            (True, '一对一服务支持'),
        ]
        advanced_3m_items = [
            (True, '内部成员', '30人'),
            (True, 'token 赠送', '2200万'),
            (True, '主流 AI 模型支持', '10+'),
            (True, '支持接入自有 AI 资源'),
            (False, '开源 AI 模型私有化部署'),
            (True, 'AI 应用接入', '无限'),
            (True, 'AI 应用提示词', '10000+'),
            (True, 'AI 知识库容量', '200G'),
            (True, '标准 AI 行业场景应用'),
            (False, '定制 AI 行业场景应用'),
            (True, 'AI 交互日志', '100万条'),
            (True, 'AI 应用及模型权限管控'),
            (True, 'AI 插件能力生态'),
            (True, '低代码 AI 工作流工具'),
            (True, '一对一服务支持'),
        ]
        advanced_1y_items = [
            (True, '内部成员', '30人'),
            (True, 'token 赠送', '7500万'),
            (True, '主流 AI 模型支持', '10+'),
            (True, '支持接入自有 AI 资源'),
            (False, '开源 AI 模型私有化部署'),
            (True, 'AI 应用接入', '无限'),
            (True, 'AI 应用提示词', '10000+'),
            (True, 'AI 知识库容量', '200G'),
            (True, '标准 AI 行业场景应用'),
            (False, '定制 AI 行业场景应用'),
            (True, 'AI 交互日志', '100万条'),
            (True, 'AI 应用及模型权限管控'),
            (True, 'AI 插件能力生态'),
            (True, '低代码 AI 工作流工具'),
            (True, '一对一服务支持'),
        ]
        privatization_1_items = [
            (True, '内部成员', '定制数量'),
            (True, 'token 赠送', '定制数量'),
            (True, '主流 AI 模型支持', '10+'),
            (True, '支持接入自有 AI 资源'),
            (True, '开源 AI 模型私有化部署'),
            (True, 'AI 应用接入', '无限'),
            (True, 'AI 应用提示词', '无限'),
            (True, 'AI 知识库容量', '无限'),
            (True, '标准 AI 行业场景应用'),
            (True, '定制 AI 行业场景应用'),
            (True, 'AI 交互日志', '无限'),
            (True, 'AI 应用及模型权限管控'),
            (True, 'AI 插件能力生态'),
            (True, '低代码 AI 工作流工具'),
            (True, '一对一服务支持'),
        ]
        # v1.9 en product items
        standard_1y_items_en = [
            (True, '内部成员', '20人'),
            (True, 'token 赠送', '4000万'),
            (True, '主流 AI 模型支持', '10+'),
            (True, '支持接入自有 AI 资源'),
            (False, '开源 AI 模型私有化部署'),
            (True, 'AI 应用接入', '20个'),
            (True, 'AI 应用提示词', '1000+'),
            (True, 'AI 知识库容量', '30G'),
            (True, '标准 AI 行业场景应用'),
            (False, '定制 AI 行业场景应用'),
            (True, 'AI 交互日志', '10万条'),
            (True, 'AI 应用及模型权限管控'),
            (False, 'AI 插件能力生态'),
            (False, '低代码 AI 工作流工具'),
            (True, '一对一服务支持'),
        ]
        pro_1y_items_en = [
            (True, '内部成员', '50人'),
            (True, 'token 赠送', '12000万'),
            (True, '主流 AI 模型支持', '10+'),
            (True, '支持接入自有 AI 资源'),
            (False, '开源 AI 模型私有化部署'),
            (True, 'AI 应用接入', '无限'),
            (True, 'AI 应用提示词', '10000+'),
            (True, 'AI 知识库容量', '200G'),
            (True, '标准 AI 行业场景应用'),
            (False, '定制 AI 行业场景应用'),
            (True, 'AI 交互日志', '100万条'),
            (True, 'AI 应用及模型权限管控'),
            (True, 'AI 插件能力生态'),
            (True, '低代码 AI 工作流工具'),
            (True, '一对一服务支持'),
        ]
        advanced_1y_items_en = [
            (True, '内部成员', '150人'),
            (True, 'token 赠送', '30000万'),
            (True, '主流 AI 模型支持', '10+'),
            (True, '支持接入自有 AI 资源'),
            (False, '开源 AI 模型私有化部署'),
            (True, 'AI 应用接入', '无限'),
            (True, 'AI 应用提示词', '20000+'),
            (True, 'AI 知识库容量', '500G'),
            (True, '标准 AI 行业场景应用'),
            (False, '定制 AI 行业场景应用'),
            (True, 'AI 交互日志', '200万条'),
            (True, 'AI 应用及模型权限管控'),
            (True, 'AI 插件能力生态'),
            (True, '低代码 AI 工作流工具'),
            (True, '一对一服务支持'),
        ]
        privatization_1_items_en = [
            (True, '内部成员', '定制数量'),
            (True, 'token 赠送', '定制数量'),
            (True, '主流 AI 模型支持', '10+'),
            (True, '支持接入自有 AI 资源'),
            (True, '开源 AI 模型私有化部署'),
            (True, 'AI 应用接入', '无限'),
            (True, 'AI 应用提示词', '无限'),
            (True, 'AI 知识库容量', '无限'),
            (True, '标准 AI 行业场景应用'),
            (True, '定制 AI 行业场景应用'),
            (True, 'AI 交互日志', '无限'),
            (True, 'AI 应用及模型权限管控'),
            (True, 'AI 插件能力生态'),
            (True, '低代码 AI 工作流工具'),
            (True, '一对一服务支持'),
        ]
        # 注意：金额以分为单位，新版已经包含了座席数
        products = [
            {
                'name': '体验版', 'category': 'preview', 'type': 'day', 'min': 7,
                'description': '7天体验，送25w token',
                'price': 1800, 'real_price': 700, 'items': person_items,
                'seats': 1,
            },
            {
                'name': '个人版', 'category': 'person', 'type': 'month', 'min': 3,
                'description': '3月起订，限1坐席，送25w token',
                'price': 2400, 'real_price': 1600, 'items': person_items,
                'seats': 1,
            },
            {
                'name': '个人版', 'category': 'person', 'type': 'year', 'min': 1,
                'description': '1年起订，限1坐席，送85w token',
                'price': 25200, 'real_price': 15360, 'items': person_items,
                'seats': 1,
            },
            {
                'name': '企业版', 'category': 'enterprise', 'type': 'month', 'min': 3,
                'description': '3月起订，10坐席起购，送250w token',
                'price': 3900, 'real_price': 2000, 'items': enterprise_items,
                'seats': 10,
            },
            {
                'name': '企业版', 'category': 'enterprise', 'type': 'year', 'min': 1,
                'description': '1年起订，10坐席起购，送1000w token',
                'price': 38400, 'real_price': 19200, 'items': enterprise_items,
                'seats': 10,
            },
            {
                'name': '私有化部署版', 'category': 'privatization', 'type': '', 'min': 1,
                'description': '沟通获取解决方案',
                'price': 99999999, 'real_price': 99999999, 'items': privatization_items,
                'seats': 99999999,
            },
            # v1.9 products
            {
                'name': '试用版', 'category': 'enterprise', 'type': 'day', 'min': 7,
                'description': '',
                'price': 0, 'real_price': 0, 'items': preview_items,
                'edition': 'preview',
                'seats': 10,
            },
            {
                'name': '初创企业版', 'category': 'enterprise', 'type': 'month', 'min': 3,
                'description': '',
                'price': 79900, 'real_price': 64900, 'items': standard_3m_items,
                'edition': 'standard',
                'seats': 10,
            },
            {
                'name': '初创企业版', 'category': 'enterprise', 'type': 'year', 'min': 1,
                'description': '',
                'price': 249900, 'real_price': 199900, 'items': standard_1y_items,
                'edition': 'standard',
                'seats': 10,
            },
            {
                'name': '中小企业版', 'category': 'enterprise', 'type': 'month', 'min': 3,
                'description': '',
                'price': 189900, 'real_price': 149900, 'items': advanced_3m_items,
                'edition': 'advanced',
                'seats': 30,
            },
            {
                'name': '中小企业版', 'category': 'enterprise', 'type': 'year', 'min': 1,
                'description': '',
                'price': 599900, 'real_price': 449900, 'items': advanced_1y_items,
                'edition': 'advanced',
                'seats': 30,
            },
            {
                'name': '定制/私有部署版', 'category': 'privatization', 'type': '', 'min': 1,
                'description': '',
                'price': 99999999, 'real_price': 99999999, 'items': privatization_1_items,
                'edition': 'privatization',
                'seats': 99999999,
            },
            # v1.9 products en
            {
                'name': 'Essential', 'category': 'enterprise', 'type': 'year', 'min': 1,
                'description': '',
                'price': 43000, 'real_price': 34300, 'items': standard_1y_items_en,
                'edition': 'standard_en',
                'seats': 20,
            },
            {
                'name': 'Pro', 'category': 'enterprise', 'type': 'year', 'min': 1,
                'description': '',
                'price': 130000, 'real_price': 104000, 'items': pro_1y_items_en,
                'edition': 'pro_en',
                'seats': 50,
            },
            {
                'name': 'Advanced', 'category': 'enterprise', 'type': 'year', 'min': 1,
                'description': '',
                'price': 321800, 'real_price': 257400, 'items': advanced_1y_items_en,
                'edition': 'advanced_en',
                'seats': 150,
            },
            {
                'name': 'Private Deployment Edition', 'category': 'privatization', 'type': '', 'min': 1,
                'description': '',
                'price': 99999999, 'real_price': 99999999, 'items': privatization_1_items_en,
                'edition': 'privatization_en',
                'seats': 99999999,
            },
        ]
        product_ids = [await self.try_insert_record(Product, ['category', 'edition', 'type'], **i) for i in products]
        logging.info("product_ids %r", product_ids)

    async def init_app_category(self):
        categories = [
            {
                'name': 'LLM',
                'description': ''
            },
            {
                'name': 'AI绘画',
                'description': ''
            },
            {
                'name': '日常办公',
                'description': ''
            },
            {
                'name': '音视频',
                'description': ''
            }
        ]
        category_ids = [await self.try_insert_record(ApplicationCategory, ['name'], **i) for i in categories]
        logging.info('category_ids %r', category_ids)

    async def run(self):
        resource_category_ids, resource_ids, model_ids = await self.init_resource()
        # 启用用户初始化逻辑
        tenant_ids, user_ids, admin_ids = await self.init_user()
        await self.init_prompt(tenant_ids[0], user_ids[0])
        await self.init_sensitive(tenant_ids, user_ids)
        await self.init_bot()
        await self.init_product()
        await self.init_app_category()

    async def migrate(self):
        await self.migrate_20230801()
        await self.migrate_20230809()
        await self.migrate_20230816()
        await self.migrate_20230821()
        await self.migrate_20230823()
        await self.migrate_20230824()
        await self.migrate_20230901()
        await self.migrate_20230906()
        await self.migrate_20230912()
        await self.migrate_20230913()
        await self.migrate_20231009()
        await self.migrate_20231013()
        await self.migrate_20231107()
        await self.migrate_20231115()
        await self.migrate_20231123()
        await self.migrate_20231128()
        await self.migrate_20231212()
        await self.migrate_20231213()
        await self.migrate_20231227()
        await self.migrate_20231229()

    async def migrate_20230801(self):
        # app_instance表增加extra字段
        sql = "alter table `app_instance` add column extra varchar(2048) not null default '{}'"
        await self.try_run_sql(sql)

    async def migrate_20230809(self):
        # resource表增加config字段
        sql = "alter table `resource` add column config varchar(2048) not null default '{}'"
        await self.try_run_sql(sql)
        sql = "alter table `tenant_resource` modify column api_key varchar(1024) not null default ''"
        await self.try_run_sql(sql)

    async def migrate_20230816(self):
        # application表增加logo字段
        sql = "alter table `application` add column logo varchar(256) not null default ''"
        await self.try_run_sql(sql)

    async def migrate_20230821(self):
        # product表增加座席
        sql = "alter table `product` add column seats int(11) not null default '0' after `min`"
        await self.try_run_sql(sql)
        # tenant_product表增加座席
        sql = "alter table `tenant_product` add column seats int(11) not null default '0' after `number`"
        await self.try_run_sql(sql)
        # tenant_product表更新旧的套餐
        sql = "update tenant_product set seats=1, modified=modified where product_id in (select id from product where category in ('preview', 'person')) and seats=0"
        await self.try_run_sql(sql)
        sql = "update tenant_product set seats=10, modified=modified where product_id in (select id from product where category in ('enterprise')) and seats=0"
        await self.try_run_sql(sql)

    async def migrate_20230823(self):
        # application表增加字段
        sql = "alter table `application` add column problem varchar(1024) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` add column video varchar(1024) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` add column manual varchar(2048) not null default ''"
        await self.try_run_sql(sql)
        # bot表增加字段
        sql = "alter table `bot` add column component varchar(256) not null default ''"
        await self.try_run_sql(sql)

    async def migrate_20230824(self):
        # application表增加字段
        sql = "alter table `application` add column video_en varchar(1024) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` add column manual_en varchar(2048) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` add column description_en varchar(2048) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` add column title_en varchar(2048) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` add column problem_en varchar(2048) not null default ''"
        await self.try_run_sql(sql)

    async def migrate_20230901(self):
        # application表增加feedback_url字段
        sql = "alter table `application` add column feedback_url varchar(256) not null default ''"
        await self.try_run_sql(sql)

    async def migrate_20230906(self):
        # bot_instance表增加crop_id字段
        sql = "alter table `bot_instance` add column crop_id varchar(128) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` add column wx_suite_id varchar(128) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` add column wx_suite_secret varchar(128) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` add column wx_suite_ticket varchar(128) not null default ''"
        await self.try_run_sql(sql)

    async def migrate_20230912(self):
        sql = "alter table `product` add column edition varchar(128) not null default ''"

    async def migrate_20230913(self):
        # 自动添加座席
        sql = "alter table `tenant` add column auto_add_seat int(11) not null default '1'"
        await self.try_run_sql(sql)

    async def migrate_20231009(self):
        # 应用是否展示到应用市场
        sql = "alter table `application` add column `show` int(11) not null default '1'"
        await self.try_run_sql(sql)

    async def migrate_20231013(self):
        # application表增加action_template字段
        sql = "alter table `application` add column action_template varchar(2048) not null default ''"
        await self.try_run_sql(sql)

    async def migrate_20231016(self):
        # application表修改字段类型
        sql = "alter table `application` modify column problem varchar(256) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` modify column video varchar(256) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` modify column manual varchar(256) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` modify column video_en varchar(256) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` modify column manual_en varchar(256) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` modify column description_en varchar(512) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` modify column title_en varchar(128) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` modify column problem_en varchar(256) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application` modify column action_template text not null"
        await self.try_run_sql(sql)

    async def migrate_20231107(self):
        # 删除application默认分类
        sql = "delete from `application_category` where name = 'default'"
        await self.try_run_sql(sql)

    async def migrate_20231103(self):
        # Tenant 表添加字段
        sql = "alter table `tenant` add column display_name varchar(128) not null default ''"
        await self.try_run_sql(sql)

    async def migrate_20231115(self):
        # Model 表增加类别
        sql = "alter table `model` add column category varchar(128) not null default ''"
        await self.try_run_sql(sql)
        # ApplicationSupportResource 表增加多资源相关字段
        sql = "alter table `application_support_resource` add column category varchar(128) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application_support_resource` add column scene varchar(128) not null default 'uni'"
        await self.try_run_sql(sql)
        sql = "alter table `application_support_resource` add column title varchar(128) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application_support_resource` add column tip varchar(256) not null default ''"
        await self.try_run_sql(sql)
        sql = "alter table `application_support_resource` add column required int(11) not null default '1'"
        await self.try_run_sql(sql)

    async def migrate_20231123(self):
        sql = "delete from `application_support_resource` where scene = ''"
        await self.try_run_sql(sql)

    async def migrate_20231128(self):
        sql = "alter table `application_support_resource` add column sorted int(11) not null default '0'"
        await self.try_run_sql(sql)

    async def migrate_20231212(self):
        users = self.session.query(Account.id, Account.tenant_id).join(
            PromptCategory,
            PromptCategory.user_id == Account.id,
            isouter=True,
        ).filter(
            PromptCategory.user_id == None
        ).all()
        logging.info('empty prompt users: %r %r', len(users), users)
        for user in users:
            await self.init_prompt(user.tenant_id, user.id)

    async def migrate_20231213(self):
        sql = "update `model` set status = -1 where name = 'gpt-4-gizmo-g-dRmGnuzWV'"
        await self.try_run_sql(sql)

    async def migrate_20231227(self):
        sql = "alter table `application` add column deploy varchar(256) not null default ''"
        await self.try_run_sql(sql)

    async def migrate_20231229(self):
        # 移除 name 为空的座席
        sql = "update `tenant_seat` set status = -1 where name = ''"
        await self.try_run_sql(sql)

    async def try_run_sql(self, sql):
        try:
            with get_engine_by_name('master').connect() as connection:
                connection.execute(text(sql))
                connection.commit()
        except Exception as e:
            logging.error(e)

    def uuid(self, content):
        return hashlib.md5((content).encode('utf-8')).hexdigest()

    async def register(self, email, passwd, tenant_name=''):
        # 如果传了tenant_name，就使用，没传就自动生成一个
        tenant_name = tenant_name or 'ai-{}'.format(self.uuid(email))
        tenant_id = await self.try_insert_record(TenantWithKey, 'name', name=tenant_name)
        user_id = await self.try_insert_record(
            Account, 'email',
            tenant_id=tenant_id,
            name=tenant_name, email=email,
            passwd=hash_password(passwd)
        )
        logging.info("user_id %r", user_id)
        admin_id = await self.try_insert_record(
            TenantAdmin,
            ['tenant_id', 'user_id'],
            tenant_id=tenant_id, user_id=user_id,
        )
        logging.info("admin_id %r", admin_id)
        # 创建提示词
        await self.init_prompt(tenant_id, user_id)
        # TODO 创建敏感词等
        return tenant_id, user_id, admin_id


async def main():
    try:
        # create database `connectai-manager` default character set utf8mb4 collate utf8mb4_unicode_ci;
        Base.metadata.create_all(get_engine_by_name('master'))
    except Exception as e:
        logging.error(e)
    model = InitData()

    # await model.migrate()
    await model.run()
    # 创建默认用户
    tenant_id, user_id, admin_id = await model.register('<EMAIL>', '123qwe', tenant_name='ai-feishu')
    print(f'Created default user: tenant_id={tenant_id}, user_id={user_id}, admin_id={admin_id}')
    # await KnowClient(tenant_id).post('/api/collection', json={'name': 'chatpdf(勿删)', 'description': 'chatpdf(勿删)'})


if __name__ == "__main__":
    IOLoop.current().run_sync(main)


