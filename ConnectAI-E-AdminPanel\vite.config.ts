import { defineConfig, loadEnv } from 'vite';
import path from 'path';
import { getServiceEnvConfig } from './.env-config';

// 直接导入构建配置
import { createViteProxy } from './build/config/proxy';
import { viteDefine } from './build/config/define';
import { setupVitePlugins } from './build/plugins/index';

/**
 * 获取项目根路径
 */
function getRootPath() {
  return path.resolve(process.cwd());
}

/**
 * 获取项目src路径
 */
function getSrcPath(srcName = 'src') {
  const rootPath = getRootPath();
  return `${rootPath}/${srcName}`;
}

export default defineConfig((configEnv) => {
  const viteEnv = loadEnv(configEnv.mode, process.cwd()) as unknown as ImportMetaEnv;

  const rootPath = getRootPath();
  const srcPath = getSrcPath();

  const isOpenProxy = viteEnv.VITE_HTTP_PROXY === 'Y';
  const isLark = viteEnv.VITE_IS_LARK === 'true';
  const envConfig = getServiceEnvConfig(viteEnv);

  return {
    base: viteEnv.VITE_BASE_URL,
    resolve: {
      alias: {
        '~': rootPath,
        '@': srcPath,
        'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js'
      }
    },
    define: viteDefine,
    plugins: setupVitePlugins(viteEnv),
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./src/styles/scss/global.scss" as *;`
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: 3200,
      open: true,
      proxy: createViteProxy(isOpenProxy, envConfig)
    },
    optimizeDeps: {
      include: [
        '@antv/data-set',
        '@antv/g2',
        '@better-scroll/core',
        // 'echarts',
        'swiper',
        'swiper/vue',
        'vditor',
        'wangeditor',
        'xgplayer'
      ]
    },
    build: {
      reportCompressedSize: false,
      sourcemap: false,
      commonjsOptions: {
        ignoreTryCatch: false
      },
      rollupOptions: {
        input: { main: isLark ? 'index_en.html' : 'index.html' },
        output: {
          manualChunks: {
            'virtual-svg-icons': ['virtual:svg-icons-register']
          }
        }
      }
    }
  };
});
