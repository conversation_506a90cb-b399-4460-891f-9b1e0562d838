# ConnectAI 完整部署指南

## 📋 概述

本指南提供了ConnectAI项目的完整部署流程，包括依赖锁定、镜像构建、服务启动和状态检查。

## 🔒 依赖管理

### 前端依赖 (ConnectAI-E-AdminPanel)
前端使用pnpm管理依赖，已通过`pnpm-lock.yaml`锁定版本：
```bash
cd ConnectAI-E-AdminPanel
pnpm install --frozen-lockfile
```

### 后端依赖
后端Python依赖通过Dockerfile直接管理，确保版本一致性：
- `manager-server/docker/Dockerfile` - 管理服务依赖（第8行）
- `DataChat-API/docker/Dockerfile` - 知识库API依赖（第8、14行）

## 🐳 Docker镜像构建

### 1. 构建管理服务镜像
```bash
docker build -t connectai-manager:local manager-server/
```

### 2. 构建知识库服务镜像
```bash
docker build -t connectai-know-server:local DataChat-API/
```

### 3. 构建原始前端管理面板镜像
```bash
docker build -t connectai-admin-panel:original ConnectAI-E-AdminPanel/
```

## 🚀 服务启动

### 启动所有服务
```bash
# 强制重新创建所有容器（推荐）
docker-compose -f docker-compose.local.yml up -d --force-recreate

# 或者普通启动
docker-compose -f docker-compose.local.yml up -d
```

### 单独重启特定服务
```bash
# 重启管理面板（强制重新创建）
docker-compose -f docker-compose.local.yml up -d --force-recreate admin-panel

# 重启管理服务
docker-compose -f docker-compose.local.yml restart manager

# 重启知识库服务
docker-compose -f docker-compose.local.yml restart know-server
```

## 🔍 服务状态检查

### 1. 检查所有容器状态
```bash
docker-compose -f docker-compose.local.yml ps
```

### 2. 检查服务健康状态
```bash
# 检查管理面板
curl -I http://localhost:8080/
Invoke-WebRequest -Uri "http://localhost:8080/" -Method Head

# 检查通过nginx代理的管理面板
curl -I http://localhost/
Invoke-WebRequest -Uri "http://localhost/" -Method Head

# 检查管理服务API
curl -I http://localhost:3000/
Invoke-WebRequest -Uri "http://localhost:3000/" -Method Head

# 检查知识库服务
curl -I http://localhost:8000/
Invoke-WebRequest -Uri "http://localhost:8000/" -Method Head
```

### 3. 检查数据库连接
```bash
# MySQL
docker exec connectai-mysql mysql -u root -proot123 -e "SHOW DATABASES;"

# Redis
docker exec connectai-redis redis-cli ping

# Elasticsearch
curl http://localhost:9200/_cluster/health
Invoke-WebRequest -Uri "http://localhost:9200/_cluster/health"

# RabbitMQ
curl -u guest:guest http://localhost:15672/api/overview
Invoke-WebRequest -Uri "http://localhost:15672/api/overview" -Headers @{Authorization="Basic Z3Vlc3Q6Z3Vlc3Q="}
```

### 4. 查看服务日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.local.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.local.yml logs admin-panel
docker-compose -f docker-compose.local.yml logs manager
docker-compose -f docker-compose.local.yml logs know-server
docker-compose -f docker-compose.local.yml logs nginx

# 实时跟踪日志
docker-compose -f docker-compose.local.yml logs -f admin-panel
```

## 🌐 访问地址

### 主要服务
- **管理面板**: http://localhost/ (通过nginx代理)
- **管理面板直接访问**: http://localhost:8080/
- **管理服务API**: http://localhost:3000/
- **知识库服务**: http://localhost:8000/

### 基础设施服务
- **MySQL**: localhost:3306 (用户名: root, 密码: root123)
- **Redis**: localhost:6379
- **Elasticsearch**: http://localhost:9200/
- **RabbitMQ管理界面**: http://localhost:15672/ (用户名: guest, 密码: guest)

## 🛠️ 故障排除

### 镜像更新不生效
当Docker镜像更新后容器没有使用新镜像时，使用强制重新创建：
```bash
docker-compose -f docker-compose.local.yml up -d --force-recreate [service-name]
```

### 端口冲突
检查端口占用：
```bash
# Windows
netstat -ano | findstr :8080
netstat -ano | findstr :3000

# 停止占用端口的进程
taskkill /PID [PID] /F
```

### 容器启动失败
检查容器日志：
```bash
docker logs [container-name]
docker-compose -f docker-compose.local.yml logs [service-name]
```

### 数据持久化问题
检查数据卷：
```bash
docker volume ls
docker volume inspect [volume-name]
```

## 📊 性能监控

### 资源使用情况
```bash
# 查看容器资源使用
docker stats

# 查看特定容器资源使用
docker stats connectai-admin-panel connectai-manager connectai-know-server
```

### 磁盘使用
```bash
# 查看Docker磁盘使用
docker system df

# 清理未使用的资源
docker system prune
```

## 🔄 更新流程

### 1. 更新代码
```bash
git pull origin main
```

### 2. 重新构建镜像
```bash
# 构建所有镜像
docker build -t connectai-manager:local manager-server/
docker build -t connectai-know-server:local DataChat-API/
docker build -t connectai-admin-panel:original ConnectAI-E-AdminPanel/
```

### 3. 重启服务
```bash
docker-compose -f docker-compose.local.yml up -d --force-recreate
```

## 📝 注意事项

1. **依赖锁定**: 所有依赖版本已锁定，避免随意更新
2. **强制重新创建**: 镜像更新后务必使用`--force-recreate`参数
3. **数据备份**: 定期备份MySQL和Elasticsearch数据
4. **日志监控**: 定期检查服务日志，及时发现问题
5. **资源监控**: 监控容器资源使用，确保系统稳定运行
