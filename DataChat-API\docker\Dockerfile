FROM python:3.8-bullseye

RUN echo "start"
RUN sed -i "s@http://deb.debian.org@http://mirrors.aliyun.com@g" /etc/apt/sources.list
RUN sed -i "s@http://security.debian.org@http://mirrors.aliyun.com@g" /etc/apt/sources.list

RUN apt-get update && apt-get install -y libcurl4-openssl-dev libffi-dev libxml2-dev g++\
  && pip3 install requests Flask gunicorn gevent bson "Flask-Session<0.5.0" Flask-SQLAlchemy ujson pycurl bcrypt elasticsearch_dsl redis PyMuPDF bs4 openai==0.28.1 flask[async] Cython flask-cors python-docx python-pptx markdown pandas openpyxl celery -i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn --no-cache-dir

ADD ./docker/entrypoint.sh /entrypoint.sh
ADD ./docker/wait-for-it.sh /wait-for-it.sh
RUN sed -i 's/\r$//' /entrypoint.sh && sed -i 's/\r$//' /wait-for-it.sh && chmod +x /entrypoint.sh && chmod +x /wait-for-it.sh

RUN pip3 install flasgger httpx langchain langchain-community -i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn --no-cache-dir

WORKDIR /server
ENTRYPOINT ["/entrypoint.sh"]

ADD ./server /server

CMD ["gunicorn", "--worker-class=gevent", "--workers", "1", "--bind", "0.0.0.0:80", "-t", "600", "--keep-alive", "60", "--log-level=debug", "server:app"]

