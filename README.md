# ConnectAI 项目管理器

这是一个基于Docker的ConnectAI项目管理器，包含了完整的微服务架构。

## 项目结构

```
project-manager/
├── ConnectAI-E-AdminPanel/     # 前端管理面板 (Vue.js)
├── ConnectAI-E-Manager/        # 后端管理服务 (Python/Tornado)
├── ConnectAI-E-Know-Server/    # 知识库服务 (Python/Flask)
├── docker-compose.local.yml   # Docker Compose配置
├── nginx.conf                  # Nginx代理配置
├── check_services.ps1          # 服务状态检查脚本
└── README.md                   # 项目说明文档
```

## 服务架构

### 核心服务
- **Manager服务** (端口3000): 主要的后端管理API服务
- **Know-Server服务** (端口8000): 知识库管理服务
- **Admin-Panel服务** (端口8080): 前端管理界面 (暂时未启用)

### 基础设施服务
- **MySQL** (端口3306): 主数据库
- **Redis** (端口6379): 缓存服务
- **Elasticsearch** (端口9200/9300): 搜索引擎
- **RabbitMQ** (端口5672/15672): 消息队列
- **Nginx** (端口80): 反向代理和负载均衡

## 快速开始

### 前置要求
- Docker Desktop
- Docker Compose
- PowerShell (Windows)

### 启动服务

1. 克隆项目并进入目录：
```bash
cd project-manager
```

2. 构建必要的Docker镜像：
```bash
# 构建Manager服务镜像
docker build -t connectai-manager:local ConnectAI-E-Manager/

# 构建Know-Server服务镜像
docker build -t connectai-know-server:local ConnectAI-E-Know-Server/
```

3. 启动所有服务：
```bash
docker-compose -f docker-compose.local.yml up -d
```

4. 检查服务状态：
```powershell
powershell -ExecutionPolicy Bypass -File check_services.ps1
```

### 访问地址

- **主页面**: http://localhost
- **管理API**: http://localhost/api/
- **知识库API**: http://localhost/know/
- **RabbitMQ管理界面**: http://localhost:15672 (用户名/密码: rabbitmq/rabbitmq)

### 直接访问服务端口

- **Manager服务**: http://localhost:3000
- **Know-Server服务**: http://localhost:8000
- **MySQL**: localhost:3306
- **Redis**: localhost:6379
- **Elasticsearch**: http://localhost:9200

## 服务管理

### 查看服务状态
```bash
docker-compose -f docker-compose.local.yml ps
```

### 查看服务日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.local.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.local.yml logs manager
docker-compose -f docker-compose.local.yml logs know-server
```

### 重启服务
```bash
# 重启所有服务
docker-compose -f docker-compose.local.yml restart

# 重启特定服务
docker-compose -f docker-compose.local.yml restart manager
```

### 停止服务
```bash
docker-compose -f docker-compose.local.yml down
```

## 环境配置

### Manager服务环境变量
- `MYSQL_HOST`: MySQL主机地址
- `MYSQL_PORT`: MySQL端口
- `MYSQL_USER`: MySQL用户名
- `MYSQL_PASSWORD`: MySQL密码
- `MYSQL_DATABASE`: MySQL数据库名
- `REDIS_HOST`: Redis主机地址
- `REDIS_PORT`: Redis端口
- `RABBITMQ_HOST`: RabbitMQ主机地址
- `RABBITMQ_PORT`: RabbitMQ端口
- `RABBITMQ_USER`: RabbitMQ用户名
- `RABBITMQ_PASSWORD`: RabbitMQ密码

### Know-Server服务环境变量
- `FLASK_OPENAI_API_KEY`: OpenAI API密钥
- `FLASK_OPENAI_API_BASE`: OpenAI API基础URL
- `FLASK_OPENAI_API_VERSION`: OpenAI API版本
- `FLASK_ES_HOST`: Elasticsearch主机地址
- `FLASK_ES_PORT`: Elasticsearch端口
- `FLASK_UPLOAD_PATH`: 文件上传路径
- `FLASK_DOMAIN`: 服务域名

## 数据持久化

项目使用Docker volumes来持久化数据：
- `mysql_data`: MySQL数据
- `redis_data`: Redis数据
- `rabbitmq_data`: RabbitMQ数据
- `elasticsearch_data`: Elasticsearch数据
- `./data/files`: 文件上传存储

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 查看服务日志：`docker-compose -f docker-compose.local.yml logs [service-name]`

2. **数据库连接失败**
   - 确保MySQL服务已启动
   - 检查数据库配置和密码

3. **前端管理面板无法访问**
   - 前端服务暂时未启用，可通过API直接访问后端服务

### 重置环境

如果需要完全重置环境：
```bash
# 停止并删除所有容器和数据
docker-compose -f docker-compose.local.yml down -v

# 重新启动
docker-compose -f docker-compose.local.yml up -d
```

## 开发说明

### 修改代码后重新部署

1. 重新构建镜像：
```bash
docker build -t connectai-manager:local ConnectAI-E-Manager/
docker build -t connectai-know-server:local ConnectAI-E-Know-Server/
```

2. 重启相关服务：
```bash
docker-compose -f docker-compose.local.yml restart manager know-server
```

### 前端开发

前端管理面板位于`ConnectAI-E-AdminPanel`目录，基于Vue.js开发。目前由于构建问题暂时未启用，可以通过以下方式本地开发：

```bash
cd ConnectAI-E-AdminPanel
npm install
npm run dev
```

## 当前状态

✅ **已完成的功能**
- Manager服务 (后端API)
- Know-Server服务 (知识库)
- MySQL数据库
- Redis缓存
- Elasticsearch搜索
- RabbitMQ消息队列
- Nginx反向代理
- 服务健康检查脚本

⚠️ **待完成的功能**
- 前端管理面板 (构建问题待解决)
- 完整的用户认证系统
- 更多API端点的测试

## 许可证

请参考各个子项目的许可证文件。
DataChat-API

ConnectAI-E-AdminPanel
Lark-Messenger-Web

ConnectAI-Helper

## 命名规则

### 分支
1. 主分支
main

2. 私有化部署标品分支
privatization

3. 某个公司定制分支
privatization-channel1

### 版本号

1. v1.0.0  (主版本)
2. v1.0.0-privatization  (私有化标品版本)
3. v1.0.0-channel1  (定制版本)

