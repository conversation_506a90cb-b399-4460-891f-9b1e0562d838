<template>
  <div class="w-full">
    <n-card
      class="h-full shadow-sm rounded-16px pt-2"
      content-style="overflow:hidden"
      header-style="padding:20px 20px 8px 20px"
    >
      <template #header>
        <n-form ref="formRef" label-placement="left" inline :label-width="80" :model="query">
          <n-form-item :label="t('message.my.name')" path="query.name">
            <n-input
              v-model:value="query.name"
              :placeholder="t('message.my.name')"
              clearable
              @keypress.enter="fetchData"
            />
          </n-form-item>
          <!-- <n-form-item :label="t('message.my.lx')" path="query.type">
            <button
              ref="statesButtonRef"
              data-dropdown-toggle="dropdown-states"
              class="flex-shrink-0 z-10 inline-flex items-center py-1.5 px-4 text-sm font-medium text-center text-gray-500 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:focus:ring-gray-700 dark:text-white dark:border-gray-600"
              type="button"
            >
              {{ currentType || t('message.my.all') }}
              <svg
                aria-hidden="true"
                class="w-4 h-4 ml-1"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </button>
            <div
              id="dropdown-states"
              ref="dropdownStatesRef"
              class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-22 dark:bg-gray-700"
            >
              <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="states-button">
                <li v-for="item in opts" :key="item.value">
                  <button
                    type="button"
                    class="inline-flex w-full px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white"
                  >
                    <div class="inline-flex items-center">
                      {{ item.label }}
                    </div>
                  </button>
                </li>
              </ul>
            </div>
          </n-form-item> -->
          <n-form-item path="values.type" class="flex">
            <n-select v-model:value="query.type" :options="opts" style="width: 100px" />
          </n-form-item>
          <n-form-item class="flex flex-1 w-full justify-end">
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center mr-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="handleToMarket"
            >
              <svg
                aria-hidden="true"
                class="w-5 h-5 mr-2 -ml-1"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"
                ></path>
              </svg>
              {{ t('message.my.xggd') }}
            </button>
          </n-form-item>
        </n-form>
        <n-divider class="p0 !my-2" />
      </template>
      <div
        class="flex content-start flex-grow-0 flex-wrap justify-start items-start flex-row mx-auto w-full h-full overflow-auto"
      >
        <div v-for="(robot, index) in data" :key="index" class="ml-[30px] mt-[30px]">
          <RobotCard :robot="robot"></RobotCard>
        </div>
      </div>
    </n-card>
    <BotInstall
      v-model:show="show"
      :client="client"
      :resource="resource"
      :app="app"
      :refresh-list="fetchData"
      :resources="resources"
    />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useDebounceFn } from '@vueuse/core';
import { Dropdown } from 'flowbite';
import { constant, orderBy } from 'lodash-es';
import { useBotStore } from '@/store';
import { useRouterPush } from '@/composables';
import { getTenantAppList, getAppCategory } from '@/service/api/app';
import { routeName } from '~/src/router';
import RobotCard from './components/robotCard.vue';
import BotInstall from './components/botInstall/index.vue';
import { t } from '@/locales';

const botStore = useBotStore();
const { installBotShow: show, app, client, resource, resources } = storeToRefs(botStore);
const { routerPush } = useRouterPush();

// const currentType = ref(t('message.my.all'));
// const opts = [
//   { label: t('message.my.all'), value: 0 },
//   { label: t('message.my.feishu'), value: 1 },
//   { label: t('message.my.dingding'), value: 2 },
//   { label: t('message.my.web'), value: 3 }
// ] as { label: string; value: any }[];

const opts = ref();
const query = ref({
  name: '',
  type: ''
});

onMounted(async () => {
  const res = await getAppCategory();
  const allOption = {
    label: t('message.my.all'),
    value: ''
  };
  opts.value = [
    allOption,
    ...(res.data?.data || []).map((item) => {
      return {
        label: item.name,
        value: item.id
      };
    })
  ];
});

const formRef = ref<any>();

const data = ref<ApiApp.Application[]>([]);
const dropdown = ref<Dropdown>();

const statesButtonRef = ref();
const dropdownStatesRef = ref();

const fetchData = async () => {
  try {
    const res = await getTenantAppList({
      page: 1,
      size: 99999,
      keyword: query.value.name,
      category_id: query.value.type
    });
    data.value = orderBy(res.data?.data, (item) => item.update_at, 'desc');
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('error', e);
  }
};

const handleSearch = useDebounceFn(fetchData, 800);
watch(() => [query.value.name, query.value.type], handleSearch);

function initDropdown() {
  dropdown.value = new Dropdown(dropdownStatesRef.value, statesButtonRef.value, { delay: 0 });
}

function handleToMarket() {
  routerPush({ name: routeName('bot_market') });
}

onMounted(() => {
  fetchData();
  initDropdown();
});
</script>

<style lang="less" scoped>
.conn-searchbar__container {
  :deep(.n-card__content) {
    padding-bottom: 0;
  }
}

.bannerShadow {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;

  &:hover {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    transition: all 0.3s ease-in-out;
  }
}
</style>
