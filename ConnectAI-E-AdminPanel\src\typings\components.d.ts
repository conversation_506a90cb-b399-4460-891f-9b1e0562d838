/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AppLoading: typeof import('./../components/common/app-loading.vue')['default']
    BetterScroll: typeof import('./../components/custom/better-scroll.vue')['default']
    CountTo: typeof import('./../components/custom/count-to.vue')['default']
    DarkModeContainer: typeof import('./../components/common/dark-mode-container.vue')['default']
    DarkModeSwitch: typeof import('./../components/common/dark-mode-switch.vue')['default']
    ExceptionBase: typeof import('./../components/common/exception-base.vue')['default']
    FbPagination: typeof import('./../components/common/fb-pagination.vue')['default']
    GithubLink: typeof import('./../components/custom/github-link.vue')['default']
    HoverContainer: typeof import('./../components/common/hover-container.vue')['default']
    IconAkarIconsChatAdd: typeof import('~icons/akar-icons/chat-add')['default']
    IconAkarIconsChatDots: typeof import('~icons/akar-icons/chat-dots')['default']
    IconAkarIconsCheck: typeof import('~icons/akar-icons/check')['default']
    IconAkarIconsCircleCheckFill: typeof import('~icons/akar-icons/circle-check-fill')['default']
    IconAkarIconsCirclePlus: typeof import('~icons/akar-icons/circle-plus')['default']
    IconAkarIconsCloudDownload: typeof import('~icons/akar-icons/cloud-download')['default']
    IconAkarIconsCloudUpload: typeof import('~icons/akar-icons/cloud-upload')['default']
    IconAkarIconsCopy: typeof import('~icons/akar-icons/copy')['default']
    IconAkarIconsCreditCardAlt1: typeof import('~icons/akar-icons/credit-card-alt1')['default']
    IconAkarIconsFile: typeof import('~icons/akar-icons/file')['default']
    IconAkarIconsMoreHorizontal: typeof import('~icons/akar-icons/more-horizontal')['default']
    IconAkarIconsPersonAdd: typeof import('~icons/akar-icons/person-add')['default']
    IconAkarIconsSave: typeof import('~icons/akar-icons/save')['default']
    IconAkarIconsSearch: typeof import('~icons/akar-icons/search')['default']
    IconAntDesignCloseOutlined: typeof import('~icons/ant-design/close-outlined')['default']
    IconAntDesignSettingOutlined: typeof import('~icons/ant-design/setting-outlined')['default']
    IconClarityNotificationLine: typeof import('~icons/clarity/notification-line')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    IconIcBaselineDoNotDisturb: typeof import('~icons/ic/baseline-do-not-disturb')['default']
    IconIcOutlineCheck: typeof import('~icons/ic/outline-check')['default']
    IconIcRoundDelete: typeof import('~icons/ic/round-delete')['default']
    IconIcRoundHdrAuto: typeof import('~icons/ic/round-hdr-auto')['default']
    IconIcRoundPlus: typeof import('~icons/ic/round-plus')['default']
    IconLineMdMenuFoldLeft: typeof import('~icons/line-md/menu-fold-left')['default']
    IconLineMdMenuUnfoldLeft: typeof import('~icons/line-md/menu-unfold-left')['default']
    IconLocalAvatar3d: typeof import('~icons/local/avatar3d')['default']
    IconLocalAvatarNotify: typeof import('~icons/local/avatar-notify')['default']
    IconLocalCheck: typeof import('~icons/local/check')['default']
    IconLocalClose: typeof import('~icons/local/close')['default']
    IconLocalInfoOut: typeof import('~icons/local/info-out')['default']
    IconLocalLoginHome: typeof import('~icons/local/login-home')['default']
    IconLocalLogo: typeof import('~icons/local/logo')['default']
    IconLocalLogoFill: typeof import('~icons/local/logo-fill')['default']
    IconLocalNetworkError: typeof import('~icons/local/network-error')['default']
    IconLocalOnWorking: typeof import('~icons/local/on-working')['default']
    IconLocalOnWorking2: typeof import('~icons/local/on-working2')['default']
    IconLocalOnWorking3: typeof import('~icons/local/on-working3')['default']
    IconLocalOnWorking4: typeof import('~icons/local/on-working4')['default']
    IconLocalReload: typeof import('~icons/local/reload')['default']
    IconMdiDrag: typeof import('~icons/mdi/drag')['default']
    IconMdiGithub: typeof import('~icons/mdi/github')['default']
    IconMdiMoonWaningCrescent: typeof import('~icons/mdi/moon-waning-crescent')['default']
    IconMdiPin: typeof import('~icons/mdi/pin')['default']
    IconMdiPinOff: typeof import('~icons/mdi/pin-off')['default']
    IconMdiRefresh: typeof import('~icons/mdi/refresh')['default']
    IconMdiWhiteBalanceSunny: typeof import('~icons/mdi/white-balance-sunny')['default']
    IconPhCaretDoubleLeftBold: typeof import('~icons/ph/caret-double-left-bold')['default']
    IconPhCaretDoubleRightBold: typeof import('~icons/ph/caret-double-right-bold')['default']
    IconSelect: typeof import('./../components/custom/icon-select.vue')['default']
    'IconUil:export': typeof import('~icons/uil/export')['default']
    ImageVerify: typeof import('./../components/custom/image-verify.vue')['default']
    LoadingEmptyWrapper: typeof import('./../components/business/loading-empty-wrapper.vue')['default']
    LoginAgreement: typeof import('./../components/business/login-agreement.vue')['default']
    MobileLoginPrompt: typeof import('./../components/common/mobile-login-prompt.vue')['default']
    NaiveProvider: typeof import('./../components/common/naive-provider.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NBackTop: typeof import('naive-ui')['NBackTop']
    NBadge: typeof import('naive-ui')['NBadge']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NCascader: typeof import('naive-ui')['NCascader']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicInput: typeof import('naive-ui')['NDynamicInput']
    NEllipsis: typeof import('naive-ui')['NEllipsis']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGridItem: typeof import('naive-ui')['NFormItemGridItem']
    NGradientText: typeof import('naive-ui')['NGradientText']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NIcon: typeof import('naive-ui')['NIcon']
    NInput: typeof import('naive-ui')['NInput']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NoDescription: typeof import('./../components/common/no-description.vue')['default']
    NoPermission: typeof import('./../components/common/no-permission.vue')['default']
    NotifySuccessLogin: typeof import('./../components/common/notify-success-login.vue')['default']
    NP: typeof import('naive-ui')['NP']
    NPagination: typeof import('naive-ui')['NPagination']
    NPopover: typeof import('naive-ui')['NPopover']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSkeleton: typeof import('naive-ui')['NSkeleton']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NThing: typeof import('naive-ui')['NThing']
    NTimePicker: typeof import('naive-ui')['NTimePicker']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTransfer: typeof import('naive-ui')['NTransfer']
    NTreeSelect: typeof import('naive-ui')['NTreeSelect']
    NUpload: typeof import('naive-ui')['NUpload']
    NUploadDragger: typeof import('naive-ui')['NUploadDragger']
    OnWorking: typeof import('./../components/common/on-working.vue')['default']
    PurchaseTip: typeof import('./../components/custom/purchase-tip.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./../components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../components/common/system-logo.vue')['default']
    TagBase: typeof import('./../components/common/tag-base.vue')['default']
    TagBot: typeof import('./../components/common/tag-bot.vue')['default']
    WebSiteLink: typeof import('./../components/custom/web-site-link.vue')['default']
  }
}
