<template>
  <n-form-item
    :label="config.label"
    :path="config.name"
    :rule="{
      required: config.required,
      validator: (rule, value) => handleValidator(value, config.deploy_name),
      trigger: ['input', 'blur']
    }"
  >
    <div class="flex flex-col w-full gap-2">
      <div v-for="option in config.options" :key="option.value" class="flex-center gap-4">
        <n-checkbox
          class="flex-1"
          :default-checked="handleDefaultChecked(option)"
          @update:checked="(checked) => handleChecked(checked, option)"
        >
          {{ option.label }}
        </n-checkbox>
        <n-input
          v-if="config.deploy_name"
          :ref="(el) => handleSetRefMap(el, option)"
          :placeholder="$t('message.dashboard.qsrbsmc')"
          class="flex-1"
          :default-value="handleDefaultValue(option)"
          clearable
          status="undefined"
          :disabled="!handleDefaultChecked(option)"
          @change="(value) => handleChange(value, option)"
        />
      </div>
    </div>
  </n-form-item>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { isEmpty } from 'lodash-es';
import { t } from '@/locales';

interface Option {
  label: string;
  value: string;
}

const props = defineProps<{
  config: any;
  data: any;
}>();

const data = reactive(props.data);

const refMap: Record<string, any> = {};

const emit = defineEmits(['change']);

function handleSetRefMap(el: any, option: Option) {
  if (el) {
    refMap[`${option.value}`] = el;
  }
}

function handleValidator(value: { value: string; deploy_name: string }[], deployName = false) {
  if (!value || value?.length === 0) {
    return new Error(t('message.dashboard.choose'));
  }

  if (deployName) {
    const valid = value.every((item: any) => item.deploy_name);
    if (!valid) return new Error(t('message.dashboard.fillin'));
  }

  return true;
}

function handleDefaultChecked(option: Option) {
  const index = data.findIndex((item: any) => item.value === option.value);
  return index !== -1;
}

function handleDefaultValue(option: Option) {
  const index = data.findIndex((item: any) => item.value === option.value);
  return data[index]?.deploy_name;
}

function handleChecked(checked: boolean, option: Option) {
  if (checked) {
    data.push({ value: option.value });
  } else {
    const index = data.findIndex((item: any) => item.value === option.value);
    data.splice(index, 1);
    if (!isEmpty(refMap)) {
      refMap[option.value].uncontrolledValue = '';
    }
  }
  handleDataChange();
}

function handleChange(value: string, option: Option) {
  const index = data.findIndex((item: any) => item.value === option.value);
  data[index].deploy_name = value;
  handleDataChange();
}

function handleDataChange() {
  emit('change', data);
}
</script>
