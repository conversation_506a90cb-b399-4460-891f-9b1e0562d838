<template>
  <div>
    <n-card
      id="market"
      class="h-full shadow-sm rounded-16px"
      content-style="overflow:hidden"
      header-style="padding:20px 20px 8px 20px"
    >
      <template #header>
        <div class="w-full flex justify-between items-center pb-2">
          <div class="text-2xl font-medium text-gray-900 dark:text-white">{{ t('message.market.xznxhd') }}</div>
          <div class="flex items-center">
            <n-form ref="formRef" label-placement="left" inline :label-width="80">
              <!-- <n-form-item path="values.type" class="flex">
                <button
                  ref="statesButtonRef"
                  data-dropdown-toggle="dropdown-states"
                  class="flex-shrink-0 z-10 inline-flex items-center py-1.5 px-4 text-sm font-medium text-center text-gray-500 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:focus:ring-gray-700 dark:text-white dark:border-gray-600"
                  type="button"
                >
                  {{ currentType || t('message.my.all') }}
                  <svg
                    aria-hidden="true"
                    class="w-4 h-4 ml-1"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </button>
                <div
                  id="dropdown-states"
                  ref="dropdownStatesRef"
                  class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-22 dark:bg-gray-700"
                >
                  <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="states-button">
                    <li v-for="item in opts" :key="item.value">
                      <button
                        type="button"
                        class="inline-flex w-full px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white"
                      >
                        <div class="inline-flex items-center">
                          {{ item.label }}
                        </div>
                      </button>
                    </li>
                  </ul>
                </div>
              </n-form-item> -->
              <n-form-item path="values.type" class="flex">
                <n-select v-model:value="values.type" :options="opts" style="width: 100px" :on-change="waitsearch" />
              </n-form-item>
              <n-form-item path="values.name" class="flex">
                <n-input
                  class="rounded-lg dark:bg-gray-700 text-gray-500 bg-gray-100 rounded-lg hover:bg-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:focus:ring-gray-700 dark:text-white dark:border-gray-600"
                  v-model:value="values.name"
                  :placeholder="t('message.my.name')"
                  clearable
                />
              </n-form-item>
              <n-form-item class="flex">
                <button @click="searchapp" class="bg-blue-700 hover:bg-blue-800 p-1 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 512 512"
                    class=""
                    fill="white"
                  >
                    <path
                      d="M344.5 298c15-23.6 23.8-51.6 23.8-81.7 0-84.1-68.1-152.3-152.1-152.3C132.1 64 64 132.2 64 216.3c0 84.1 68.1 152.3 152.1 152.3 30.5 0 58.9-9 82.7-24.4l6.9-4.8L414.3 448l33.7-34.3-108.5-108.6 5-7.1zm-43.1-166.8c22.7 22.7 35.2 52.9 35.2 85s-12.5 62.3-35.2 85c-22.7 22.7-52.9 35.2-85 35.2s-62.3-12.5-85-35.2c-22.7-22.7-35.2-52.9-35.2-85s12.5-62.3 35.2-85c22.7-22.7 52.9-35.2 85-35.2s62.3 12.5 85 35.2z"
                    />
                  </svg>
                </button>
              </n-form-item>
            </n-form>
          </div>
        </div>
        <n-divider class="p0 !my-2" />
      </template>
      <div
        class="flex content-start flex-grow-0 flex-wrap justify-center gap-x-12 gap-y-14 items-start flex-row mx-auto w-full h-full overflow-auto"
      >
        <Card
          v-for="(item, index) in data"
          :id="item.id"
          :key="index"
          :tenant-status="item.tenant_status"
          :title="isLark ? item.title_en : item.title"
          :price="item.price"
          :description="isLark ? item.description_en : item.description"
          :name="item.name"
          :icon="item.icon"
          :support_bot="item.support_bot"
          class=""
          @click="() => handleDetail(item.id)"
          @handle-buy="() => handleBuy(item)"
          @handle-install="() => handleInstall(item)"
        />
        <div class="w-full flex justify-center">
          <MoreBot />
        </div>
      </div>
    </n-card>
    <n-drawer
      v-model:show="showDetail"
      :width="200"
      :height="detailHeight"
      :default-height="calcHeight()"
      placement="bottom"
      :resizable="false"
      :trap-focus="false"
      :show-mask="true"
      :block-scroll="false"
      @after-leave="clearAppId()"
    >
      <detail
        :id="activeId"
        :btntype="btntype"
        ref="detailPanel"
        :is-full-screen="isFullScreen"
        @handle-buy="handleBuyById(activeId)"
        @close="showDetail = false"
        @full-screen="handleFullScreen"
        @min-screen="handleMinScreen"
      />
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import { routeName } from '@/router';
import { useBotStore } from '@/store';
import { useRouterPush, useUrl } from '@/composables';
import { useTenantPrivilege } from '@/hooks';
import { isLark } from '@/utils';
import {
  getAppClient,
  getAppList,
  getAppResource,
  updateAppStatus,
  getAppCategory,
  getAppDetail,
  getAppResources
} from '@/service/api/app';
import Detail from '@/views/bot/market/components/detail.vue';
import { t } from '@/locales';
import Card from './components/botCard_.vue';
import MoreBot from './components/moreBot.vue';

// const currentType = ref(t('message.my.all'));
const opts = ref();
const values = ref({
  name: '',
  type: ''
});

function waitsearch() {
  setTimeout(() => {
    searchapp();
  }, 100);
}

function searchapp() {
  console.log(values.value);
  getList(values.value.name, values.value.type);
}

const { isSubscribe } = useTenantPrivilege();
const botStore = useBotStore();
const { setApp, setAppClient, setAppResource, setInstallBotShow, setAppResources } = botStore;

const { routerPush, router } = useRouterPush();
const { clearAppId, replaceNowAppId, addFullPage, removeFullPage } = useUrl();

const keyword = ref('');

const message = useMessage();

const detailHeight = ref<any>(undefined);
const calcHeight = (ratio = 0.75) => {
  const height = window.innerHeight;
  return height * ratio;
};
const isFullScreen = computed(() => {
  if (detailHeight.value === undefined) {
    return false;
  }
  return detailHeight.value >= calcHeight(0.8);
});

const handleFullScreen = () => {
  detailHeight.value = calcHeight(1);
  addFullPage();
};

const handleMinScreen = () => {
  detailHeight.value = calcHeight(0.75);
  removeFullPage();
};

const mockData: ApiApp.Application[] = [];

const data = ref<ApiApp.Application[]>();
const activeId = ref('');
const btntype = ref(null);

const activeBot = computed(() => {
  return data.value?.find((item) => item.id === activeId.value);
});
const showDetail = ref(false);

const handleDetail = async (id: string) => {
  showDetail.value = true;
  activeId.value = id;
  const btn = await getAppDetail(id);
  btntype.value = btn.data?.data.tenant_status;
  replaceNowAppId(id);
};

async function getList(keyword?: string, category_id?: string) {
  try {
    const res = await getAppList({ page: 1, size: 99999, keyword, category_id });
    data.value = (res.data?.data || []).concat(mockData);
    // console.log(data.value);
  } catch (err) {}
}

async function handleBuy({ id }: ApiApp.Application) {
  await updateAppStatus({ id, action: 'buy' });
  message.success(t('message.msg.gmcg'));
  getList();
}
async function handleBuyById(id: string) {
  await updateAppStatus({ id, action: 'buy' });
  message.success(t('message.msg.gmcg'));
  getList();
  await routerPush({ name: routeName('bot_info'), query: { id: id as any } });
}

async function handleInstall(item: ApiApp.Application) {
  const {
    data: { data: clientData }
  } = await getAppClient({ id: item.id });
  const {
    data: { data: resourceData }
  } = await getAppResource({ id: item.id });
  const {
    data: { data: resourcesData }
  } = await getAppResources({ id: item.id });
  setApp(item);
  setAppClient(clientData);
  setAppResource(resourceData);
  setAppResources(resourcesData);
  setInstallBotShow(true);
  routerPush({
    name: routeName('bot_my')
  });
}

function handleSearch(e: Event) {
  e.preventDefault();
  getList(keyword.value);
}

const showDashboardPricingKey = 'show_dashboard_pricing';

function showSubscribe() {
  return Boolean(sessionStorage.getItem(showDashboardPricingKey));
}

onMounted(async () => {
  await getList();
  if (!isLark && !isSubscribe() && !showSubscribe()) {
    routerPush({ name: 'dashboard_pricing' }, false);
    sessionStorage.setItem(showDashboardPricingKey, 1);
  }
});
const { query, fullPath } = useRoute();

onMounted(() => {
  // 获取路由
  const idMatch = /id=([^&]+)/.exec(fullPath);
  const id = idMatch?.[1];
  const fullPage = new RegExp(/fullPage=(\S*)/).exec(fullPath)?.[1];
  if (!id) return;
  if (id) {
    handleDetail(id);
  }
  if (fullPage === 'true') {
    handleFullScreen();
  } else {
    handleMinScreen();
  }
});

onMounted(async () => {
  const res = await getAppCategory();
  const allOption = {
    label: t('message.my.all'),
    value: ''
  };
  opts.value = [
    allOption,
    ...(res.data?.data || []).map((item) => {
      return {
        label: item.name,
        value: item.id
      };
    })
  ];
});
</script>

<style lang="scss">
#market {
  ::-webkit-scrollbar {
    display: none;
  }
}
</style>
